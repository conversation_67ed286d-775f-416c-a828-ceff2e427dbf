import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import glob
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette('husl')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

# Support Chinese fonts
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# Define the results directory
results_dir = '/home/<USER>/projects/styling_agent/api/styling_agent/script_data/feedback_results/2025-08-07_14'

# Find all Excel analysis files
excel_files = glob.glob(os.path.join(results_dir, '*_analysis.xlsx'))
print(f'Found {len(excel_files)} analysis files:')
for file in excel_files:
    print(f'  - {os.path.basename(file)}')

def extract_document_name(filename):
    """Extract clean document name from filename"""
    basename = os.path.basename(filename)
    # Remove _analysis.xlsx suffix
    name = basename.replace('_analysis.xlsx', '')
    # Remove timestamp suffixes
    import re
    name = re.sub(r'_original_\d+', '', name)
    # Truncate long names for display
    if len(name) > 50:
        name = name[:47] + '...'
    return name

def calculate_accuracy_metrics(tp, fp, fn):
    """Calculate accuracy metrics from TP, FP, FN"""
    # Feedback Accuracy (used in evaluation)
    feedback_accuracy = tp / (tp + fp) if (tp + fp) > 0 else 0
    
    # Overall Accuracy (Precision)
    overall_accuracy = tp / (tp + fp) if (tp + fp) > 0 else 0
    
    # Optimistic Accuracy (Recall)
    optimistic_accuracy = tp / (tp + fn) if (tp + fn) > 0 else 0
    
    return feedback_accuracy, overall_accuracy, optimistic_accuracy

# Load and consolidate all analysis results
all_results = []

for excel_file in excel_files:
    try:
        # Read the Summary sheet
        df_summary = pd.read_excel(excel_file, sheet_name='Summary')
        
        # Extract metrics
        metrics = {}
        for _, row in df_summary.iterrows():
            metrics[row['Metric']] = row['Value']
        
        # Convert to numeric values
        total_ai_comments = int(metrics.get('Total AI Comments', 0))
        total_user_comments = int(metrics.get('Total User Comments', 0))
        tp = int(metrics.get('True Positives (TP)', 0))
        fp = int(metrics.get('False Positives (FP)', 0))
        fn = int(metrics.get('False Negatives (FN)', 0))
        
        # Calculate accuracy metrics
        feedback_acc, overall_acc, optimistic_acc = calculate_accuracy_metrics(tp, fp, fn)
        
        # Store results
        result = {
            'Document': extract_document_name(excel_file),
            'Total_AI_Comments': total_ai_comments,
            'Total_User_Comments': total_user_comments,
            'True_Positives': tp,
            'False_Positives': fp,
            'False_Negatives': fn,
            'Feedback_Accuracy': feedback_acc,
            'Overall_Accuracy': overall_acc,
            'Optimistic_Accuracy': optimistic_acc,
            'File_Path': excel_file
        }
        all_results.append(result)
        
    except Exception as e:
        print(f'Error processing {excel_file}: {e}')

# Create DataFrame
results_df = pd.DataFrame(all_results)
print(f'Successfully processed {len(results_df)} documents')
print('\nSummary of results:')
print(results_df[['Document', 'Total_AI_Comments', 'Total_User_Comments', 'Feedback_Accuracy']].head())

# Display overall statistics
print('=== 整体统计概览 ===')
print(f'总文档数量: {len(results_df)}')
print(f'总 AI 评论数: {results_df["Total_AI_Comments"].sum()}')
print(f'总用户评论数: {results_df["Total_User_Comments"].sum()}')
print(f'总 True Positives: {results_df["True_Positives"].sum()}')
print(f'总 False Positives: {results_df["False_Positives"].sum()}')
print(f'总 False Negatives: {results_df["False_Negatives"].sum()}')
print(f'平均 Feedback Accuracy: {results_df["Feedback_Accuracy"].mean():.3f}')
print(f'平均 Overall Accuracy: {results_df["Overall_Accuracy"].mean():.3f}')
print(f'平均 Optimistic Accuracy: {results_df["Optimistic_Accuracy"].mean():.3f}')

# Create a detailed summary table
summary_table = results_df[[
    'Document', 'Total_AI_Comments', 'Total_User_Comments', 
    'True_Positives', 'False_Positives', 'False_Negatives',
    'Feedback_Accuracy', 'Overall_Accuracy', 'Optimistic_Accuracy'
]].copy()

# Round accuracy values for better display
for col in ['Feedback_Accuracy', 'Overall_Accuracy', 'Optimistic_Accuracy']:
    summary_table[col] = summary_table[col].round(3)

print('\n=== 详细统计表 ===')
print(summary_table.to_string(index=False))

# Create accuracy comparison chart
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Word 文档反馈统计分析 - 准确率指标对比', fontsize=16, fontweight='bold')

# 1. Accuracy metrics comparison (bar chart)
ax1 = axes[0, 0]
accuracy_data = results_df[['Feedback_Accuracy', 'Overall_Accuracy', 'Optimistic_Accuracy']]
x_pos = np.arange(len(results_df))
width = 0.25

ax1.bar(x_pos - width, accuracy_data['Feedback_Accuracy'], width, label='Feedback Accuracy', alpha=0.8)
ax1.bar(x_pos, accuracy_data['Overall_Accuracy'], width, label='Overall Accuracy', alpha=0.8)
ax1.bar(x_pos + width, accuracy_data['Optimistic_Accuracy'], width, label='Optimistic Accuracy', alpha=0.8)

ax1.set_xlabel('文档')
ax1.set_ylabel('准确率')
ax1.set_title('各文档准确率指标对比')
ax1.set_xticks(x_pos)
ax1.set_xticklabels([f'Doc{i+1}' for i in range(len(results_df))], rotation=45)
ax1.legend()
ax1.grid(True, alpha=0.3)

# 2. Average accuracy metrics (pie chart)
ax2 = axes[0, 1]
avg_accuracies = [
    results_df['Feedback_Accuracy'].mean(),
    results_df['Overall_Accuracy'].mean(),
    results_df['Optimistic_Accuracy'].mean()
]
labels = ['Feedback Accuracy', 'Overall Accuracy', 'Optimistic Accuracy']
colors = ['#ff9999', '#66b3ff', '#99ff99']

wedges, texts, autotexts = ax2.pie(avg_accuracies, labels=labels, colors=colors, autopct='%1.3f', startangle=90)
ax2.set_title('平均准确率指标分布')

# 3. Comments distribution
ax3 = axes[1, 0]
ax3.scatter(results_df['Total_AI_Comments'], results_df['Total_User_Comments'], 
           c=results_df['Feedback_Accuracy'], cmap='viridis', s=100, alpha=0.7)
ax3.set_xlabel('AI 评论数量')
ax3.set_ylabel('用户评论数量')
ax3.set_title('AI 评论 vs 用户评论 (颜色表示 Feedback Accuracy)')
ax3.grid(True, alpha=0.3)
cbar = plt.colorbar(ax3.collections[0], ax=ax3)
cbar.set_label('Feedback Accuracy')

# 4. TP, FP, FN distribution
ax4 = axes[1, 1]
categories = ['True Positives', 'False Positives', 'False Negatives']
totals = [
    results_df['True_Positives'].sum(),
    results_df['False_Positives'].sum(),
    results_df['False_Negatives'].sum()
]
colors = ['#2ecc71', '#e74c3c', '#f39c12']

bars = ax4.bar(categories, totals, color=colors, alpha=0.8)
ax4.set_ylabel('数量')
ax4.set_title('总体 TP/FP/FN 分布')
ax4.grid(True, alpha=0.3)

# Add value labels on bars
for bar, value in zip(bars, totals):
    ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
             str(value), ha='center', va='bottom', fontweight='bold')

plt.tight_layout()
plt.show()

# Create document performance ranking
fig, axes = plt.subplots(1, 2, figsize=(16, 6))
fig.suptitle('文档性能排名分析', fontsize=16, fontweight='bold')

# Sort by Feedback Accuracy
sorted_df = results_df.sort_values('Feedback_Accuracy', ascending=True)

# 1. Feedback Accuracy ranking
ax1 = axes[0]
y_pos = np.arange(len(sorted_df))
bars = ax1.barh(y_pos, sorted_df['Feedback_Accuracy'], alpha=0.8, 
                color=plt.cm.RdYlGn(sorted_df['Feedback_Accuracy']))
ax1.set_yticks(y_pos)
ax1.set_yticklabels([f'Doc{i+1}' for i in range(len(sorted_df))], fontsize=8)
ax1.set_xlabel('Feedback Accuracy')
ax1.set_title('文档 Feedback Accuracy 排名')
ax1.grid(True, alpha=0.3)

# Add value labels
for i, (bar, value) in enumerate(zip(bars, sorted_df['Feedback_Accuracy'])):
    ax1.text(value + 0.01, bar.get_y() + bar.get_height()/2, 
             f'{value:.3f}', va='center', fontsize=8)

# 2. Comments volume vs accuracy
ax2 = axes[1]
total_comments = results_df['Total_AI_Comments'] + results_df['Total_User_Comments']
scatter = ax2.scatter(total_comments, results_df['Feedback_Accuracy'], 
                     s=results_df['True_Positives']*10, alpha=0.6, 
                     c=results_df['False_Positives'], cmap='Reds')
ax2.set_xlabel('总评论数 (AI + 用户)')
ax2.set_ylabel('Feedback Accuracy')
ax2.set_title('评论数量 vs 准确率 (气泡大小=TP, 颜色=FP)')
ax2.grid(True, alpha=0.3)

# Add colorbar
cbar = plt.colorbar(scatter, ax=ax2)
cbar.set_label('False Positives')

plt.tight_layout()
plt.show()

# Statistical distribution analysis
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('统计分布分析', fontsize=16, fontweight='bold')

# 1. Accuracy distribution histogram
ax1 = axes[0, 0]
ax1.hist(results_df['Feedback_Accuracy'], bins=10, alpha=0.7, color='skyblue', edgecolor='black')
ax1.axvline(results_df['Feedback_Accuracy'].mean(), color='red', linestyle='--', 
           label=f'Mean: {results_df["Feedback_Accuracy"].mean():.3f}')
ax1.axvline(results_df['Feedback_Accuracy'].median(), color='green', linestyle='--', 
           label=f'Median: {results_df["Feedback_Accuracy"].median():.3f}')
ax1.set_xlabel('Feedback Accuracy')
ax1.set_ylabel('频次')
ax1.set_title('Feedback Accuracy 分布')
ax1.legend()
ax1.grid(True, alpha=0.3)

# 2. Comments distribution
ax2 = axes[0, 1]
ax2.hist(results_df['Total_AI_Comments'], bins=10, alpha=0.7, label='AI Comments', color='orange')
ax2.hist(results_df['Total_User_Comments'], bins=10, alpha=0.7, label='User Comments', color='purple')
ax2.set_xlabel('评论数量')
ax2.set_ylabel('频次')
ax2.set_title('评论数量分布')
ax2.legend()
ax2.grid(True, alpha=0.3)

# 3. Box plot of accuracy metrics
ax3 = axes[1, 0]
accuracy_data = [results_df['Feedback_Accuracy'], results_df['Overall_Accuracy'], results_df['Optimistic_Accuracy']]
box_plot = ax3.boxplot(accuracy_data, labels=['Feedback', 'Overall', 'Optimistic'], patch_artist=True)
colors = ['lightblue', 'lightgreen', 'lightcoral']
for patch, color in zip(box_plot['boxes'], colors):
    patch.set_facecolor(color)
ax3.set_ylabel('准确率')
ax3.set_title('准确率指标箱线图')
ax3.grid(True, alpha=0.3)

# 4. Correlation heatmap
ax4 = axes[1, 1]
correlation_cols = ['Total_AI_Comments', 'Total_User_Comments', 'True_Positives', 
                   'False_Positives', 'False_Negatives', 'Feedback_Accuracy']
corr_matrix = results_df[correlation_cols].corr()
im = ax4.imshow(corr_matrix, cmap='coolwarm', aspect='auto', vmin=-1, vmax=1)
ax4.set_xticks(range(len(correlation_cols)))
ax4.set_yticks(range(len(correlation_cols)))
ax4.set_xticklabels([col.replace('_', '\n') for col in correlation_cols], rotation=45, ha='right')
ax4.set_yticklabels([col.replace('_', '\n') for col in correlation_cols])
ax4.set_title('指标相关性热力图')

# Add correlation values
for i in range(len(correlation_cols)):
    for j in range(len(correlation_cols)):
        text = ax4.text(j, i, f'{corr_matrix.iloc[i, j]:.2f}', 
                       ha='center', va='center', color='black', fontsize=8)

plt.colorbar(im, ax=ax4, shrink=0.8)
plt.tight_layout()
plt.show()

# Generate key findings and summary
print('=== 关键发现和总结 ===')
print()

# Basic statistics
total_docs = len(results_df)
avg_feedback_acc = results_df['Feedback_Accuracy'].mean()
std_feedback_acc = results_df['Feedback_Accuracy'].std()
best_doc_idx = results_df['Feedback_Accuracy'].idxmax()
worst_doc_idx = results_df['Feedback_Accuracy'].idxmin()

print(f'📊 基本统计:')
print(f'   • 分析文档总数: {total_docs}')
print(f'   • 平均 Feedback Accuracy: {avg_feedback_acc:.3f} (±{std_feedback_acc:.3f})')
print(f'   • 最佳表现文档: {results_df.loc[best_doc_idx, "Document"]} ({results_df.loc[best_doc_idx, "Feedback_Accuracy"]:.3f})')
print(f'   • 最差表现文档: {results_df.loc[worst_doc_idx, "Document"]} ({results_df.loc[worst_doc_idx, "Feedback_Accuracy"]:.3f})')
print()

# Performance categories
high_perf = results_df[results_df['Feedback_Accuracy'] >= 0.8]
medium_perf = results_df[(results_df['Feedback_Accuracy'] >= 0.6) & (results_df['Feedback_Accuracy'] < 0.8)]
low_perf = results_df[results_df['Feedback_Accuracy'] < 0.6]

print(f'🎯 性能分类:')
print(f'   • 高性能文档 (≥0.8): {len(high_perf)} 个 ({len(high_perf)/total_docs*100:.1f}%)')
print(f'   • 中等性能文档 (0.6-0.8): {len(medium_perf)} 个 ({len(medium_perf)/total_docs*100:.1f}%)')
print(f'   • 低性能文档 (<0.6): {len(low_perf)} 个 ({len(low_perf)/total_docs*100:.1f}%)')
print()

# Comments analysis
total_ai_comments = results_df['Total_AI_Comments'].sum()
total_user_comments = results_df['Total_User_Comments'].sum()
avg_ai_comments = results_df['Total_AI_Comments'].mean()
avg_user_comments = results_df['Total_User_Comments'].mean()

print(f'💬 评论分析:')
print(f'   • 总 AI 评论数: {total_ai_comments} (平均: {avg_ai_comments:.1f}/文档)')
print(f'   • 总用户评论数: {total_user_comments} (平均: {avg_user_comments:.1f}/文档)')
print(f'   • AI/用户评论比例: {total_ai_comments/total_user_comments:.2f}:1')
print()

# Accuracy metrics comparison
feedback_acc_avg = results_df['Feedback_Accuracy'].mean()
overall_acc_avg = results_df['Overall_Accuracy'].mean()
optimistic_acc_avg = results_df['Optimistic_Accuracy'].mean()

print(f'📈 准确率指标对比:')
print(f'   • Feedback Accuracy (主要评估指标): {feedback_acc_avg:.3f}')
print(f'   • Overall Accuracy (精确率): {overall_acc_avg:.3f}')
print(f'   • Optimistic Accuracy (召回率): {optimistic_acc_avg:.3f}')
print()

# Error analysis
total_tp = results_df['True_Positives'].sum()
total_fp = results_df['False_Positives'].sum()
total_fn = results_df['False_Negatives'].sum()

print(f'❌ 错误分析:')
print(f'   • True Positives: {total_tp}')
print(f'   • False Positives: {total_fp} ({total_fp/(total_tp+total_fp)*100:.1f}% of AI predictions)')
print(f'   • False Negatives: {total_fn} ({total_fn/(total_tp+total_fn)*100:.1f}% of actual positives)')
print()

# Recommendations
print(f'💡 建议:')
if avg_feedback_acc < 0.7:
    print('   • 整体准确率偏低，建议优化模型或调整评估标准')
if total_fp > total_fn:
    print('   • False Positives 较多，建议提高模型精确度')
elif total_fn > total_fp:
    print('   • False Negatives 较多，建议提高模型召回率')
if std_feedback_acc > 0.2:
    print('   • 文档间性能差异较大，建议分析高性能文档的特征')
print('   • 建议重点关注低性能文档，分析失败原因')
print('   • 可考虑根据文档类型或复杂度进行分类评估')

# Export consolidated results
from datetime import datetime

# Create export directory
export_dir = '/home/<USER>/projects/styling_agent/api/styling_agent/script_data/feedback_results'
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
export_filename = f'consolidated_analysis_{timestamp}.xlsx'
export_path = os.path.join(export_dir, export_filename)

# Prepare data for export
export_df = results_df.copy()
export_df = export_df.drop('File_Path', axis=1)  # Remove file path for cleaner export

# Create summary statistics
summary_stats = pd.DataFrame({
    'Metric': [
        'Total Documents',
        'Total AI Comments',
        'Total User Comments', 
        'Total True Positives',
        'Total False Positives',
        'Total False Negatives',
        'Average Feedback Accuracy',
        'Average Overall Accuracy',
        'Average Optimistic Accuracy',
        'Std Feedback Accuracy',
        'Best Document Feedback Accuracy',
        'Worst Document Feedback Accuracy'
    ],
    'Value': [
        len(results_df),
        results_df['Total_AI_Comments'].sum(),
        results_df['Total_User_Comments'].sum(),
        results_df['True_Positives'].sum(),
        results_df['False_Positives'].sum(),
        results_df['False_Negatives'].sum(),
        results_df['Feedback_Accuracy'].mean(),
        results_df['Overall_Accuracy'].mean(),
        results_df['Optimistic_Accuracy'].mean(),
        results_df['Feedback_Accuracy'].std(),
        results_df['Feedback_Accuracy'].max(),
        results_df['Feedback_Accuracy'].min()
    ]
})

# Export to Excel with multiple sheets
with pd.ExcelWriter(export_path, engine='openpyxl') as writer:
    summary_stats.to_excel(writer, sheet_name='Summary Statistics', index=False)
    export_df.to_excel(writer, sheet_name='Detailed Results', index=False)
    
    # Add performance categories
    high_perf.to_excel(writer, sheet_name='High Performance', index=False)
    medium_perf.to_excel(writer, sheet_name='Medium Performance', index=False)
    low_perf.to_excel(writer, sheet_name='Low Performance', index=False)

print(f'✅ 结果已导出到: {export_path}')
print(f'📊 包含 {len(export_df)} 个文档的详细分析结果')
print('\n导出文件包含以下工作表:')
print('  • Summary Statistics: 整体统计摘要')
print('  • Detailed Results: 所有文档的详细结果')
print('  • High Performance: 高性能文档 (Feedback Accuracy ≥ 0.8)')
print('  • Medium Performance: 中等性能文档 (0.6 ≤ Feedback Accuracy < 0.8)')
print('  • Low Performance: 低性能文档 (Feedback Accuracy < 0.6)')