#!/usr/bin/env python3
"""Multi-Date Feedback Analysis Tool - 多日期反馈统计分析工具

这个工具用于分析多个日期/小时的Excel统计文件，对比不同时间点的反馈情况：

- 读取日期目录（YYYY-MM-DD）或小时目录（YYYY-MM-DD_HH）下的Excel分析文件
- 比较不同时间点间的反馈变化趋势
- 生成综合统计报告
- 支持Excel和JSON格式输出

Usage:
    python multi_version_analyzer.py <feedback_directory> [options]
"""

import argparse
import json
import sys
from collections import defaultdict
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional


from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font, PatternFill
from openpyxl.utils import get_column_letter


@dataclass
class FeedbackData:
    """Data class for feedback analysis results"""

    date: str
    document_name: str
    total_comments: int
    comments_with_feedback: int
    correct_modifications: int
    incorrect_modifications: int
    accuracy_rate: float
    feedback_coverage: float
    file_path: str


@dataclass
class DocumentTrend:
    """Data class for document trend analysis"""

    document_name: str
    feedback_data: List[FeedbackData]
    trend_direction: str  # 'improving', 'declining', 'stable'
    improvement_score: float


class MultiFeedbackAnalyzer:
    """Multi-date feedback analyzer"""

    def __init__(self, feedback_directory: str):
        self.feedback_dir = Path(feedback_directory)
        self.feedback_data = []
        self.document_trends = {}
        self.date_analysis = {}

        if not self.feedback_dir.exists():
            raise FileNotFoundError(
                f"Feedback directory not found: {feedback_directory}"
            )

    def scan_feedback_files(self) -> List[FeedbackData]:
        """Scan all Excel analysis files in date directories"""
        print("🔍 扫描反馈文件...")

        feedback_data = []

        # Find all date directories (supports both YYYY-MM-DD and YYYY-MM-DD_HH formats)
        date_dirs = [
            d
            for d in self.feedback_dir.iterdir()
            if d.is_dir()
            and (d.name.count("-") == 2 and ("_" in d.name or len(d.name) == 10))
        ]
        date_dirs.sort()

        print(f"📅 发现 {len(date_dirs)} 个日期目录: {[d.name for d in date_dirs]}")

        for date_dir in date_dirs:
            date_str = date_dir.name
            print(f"\n📁 处理日期: {date_str}")

            # Find all Excel analysis files
            excel_files = list(date_dir.glob("*_analysis.xlsx"))
            print(f"  📊 发现 {len(excel_files)} 个Excel文件")

            for excel_file in excel_files:
                try:
                    # Extract document name from filename
                    doc_name = excel_file.name.replace("_analysis.xlsx", "")

                    # Read Excel file
                    data = self._read_excel_analysis(excel_file)
                    if data:
                        feedback_data.append(
                            FeedbackData(
                                date=date_str,
                                document_name=doc_name,
                                total_comments=data["total_comments"],
                                comments_with_feedback=data["comments_with_feedback"],
                                correct_modifications=data["correct_modifications"],
                                incorrect_modifications=data["incorrect_modifications"],
                                accuracy_rate=data["accuracy_rate"],
                                feedback_coverage=data["feedback_coverage"],
                                file_path=str(excel_file),
                            )
                        )
                        print(
                            f"    ✅ {doc_name}: {data['total_comments']} 评论, {data['accuracy_rate']:.1f}% 准确率"
                        )
                    else:
                        print(f"    ❌ 无法读取: {excel_file.name}")

                except Exception as e:
                    print(f"    ❌ 处理失败 {excel_file.name}: {e}")

        self.feedback_data = feedback_data
        print(f"\n📊 总共加载了 {len(feedback_data)} 条反馈数据")
        return feedback_data

    def _read_excel_analysis(self, excel_file: Path) -> Optional[Dict]:
        """Read Excel analysis file and extract summary data"""
        try:
            wb = load_workbook(excel_file)

            # Try to find Summary sheet
            if "Summary" in wb.sheetnames:
                ws = wb["Summary"]
            else:
                # Use first sheet if no Summary sheet
                ws = wb.active

            # Extract data from Summary sheet
            data = {}
            for row in range(1, ws.max_row + 1):
                metric_cell = ws.cell(row=row, column=1)
                value_cell = ws.cell(row=row, column=2)

                if metric_cell.value and value_cell.value is not None:
                    metric = str(metric_cell.value).strip()
                    value = value_cell.value

                    # Map metric names to standard keys
                    if "Total Comments" in metric:
                        data["total_comments"] = (
                            int(value) if isinstance(value, (int, float)) else 0
                        )
                    elif "Comments With Feedback" in metric:
                        data["comments_with_feedback"] = (
                            int(value) if isinstance(value, (int, float)) else 0
                        )
                    elif "Correct Modifications" in metric:
                        data["correct_modifications"] = (
                            int(value) if isinstance(value, (int, float)) else 0
                        )
                    elif "Incorrect Modifications" in metric:
                        data["incorrect_modifications"] = (
                            int(value) if isinstance(value, (int, float)) else 0
                        )
                    elif "Accuracy Rate" in metric:
                        data["accuracy_rate"] = (
                            float(value) if isinstance(value, (int, float)) else 0.0
                        )
                    elif "Feedback Coverage" in metric:
                        data["feedback_coverage"] = (
                            float(value) if isinstance(value, (int, float)) else 0.0
                        )

            # Calculate missing metrics if not present
            if "accuracy_rate" not in data:
                total_mods = data.get("correct_modifications", 0) + data.get(
                    "incorrect_modifications", 0
                )
                data["accuracy_rate"] = (
                    (data.get("correct_modifications", 0) / total_mods * 100)
                    if total_mods > 0
                    else 0.0
                )

            # Calculate three types of accuracy
            total_comments = data.get("total_comments", 0)
            comments_with_feedback = data.get("comments_with_feedback", 0)
            correct_modifications = data.get("correct_modifications", 0)
            comments_without_feedback = total_comments - comments_with_feedback

            # 1. Feedback Accuracy - only considers comments with feedback
            data["feedback_accuracy"] = (
                (correct_modifications / comments_with_feedback * 100)
                if comments_with_feedback > 0
                else 0.0
            )

            # 2. Overall Accuracy - conservative estimate (no feedback = incorrect)
            data["overall_accuracy"] = (
                (correct_modifications / total_comments * 100)
                if total_comments > 0
                else 0.0
            )

            # 3. Optimistic Accuracy - optimistic estimate (no feedback = correct)
            data["optimistic_accuracy"] = (
                (
                    (correct_modifications + comments_without_feedback)
                    / total_comments
                    * 100
                )
                if total_comments > 0
                else 0.0
            )

            if "feedback_coverage" not in data:
                total_comments = data.get("total_comments", 0)
                data["feedback_coverage"] = (
                    (data.get("comments_with_feedback", 0) / total_comments * 100)
                    if total_comments > 0
                    else 0.0
                )

            # Ensure all required fields exist
            required_fields = [
                "total_comments",
                "comments_with_feedback",
                "correct_modifications",
                "incorrect_modifications",
                "accuracy_rate",
                "feedback_coverage",
            ]
            for field in required_fields:
                if field not in data:
                    data[field] = (
                        0
                        if field != "accuracy_rate" and field != "feedback_coverage"
                        else 0.0
                    )

            return data

        except Exception as e:
            print(f"Error reading {excel_file}: {e}")
            return None

    def analyze_trends(self) -> Dict[str, DocumentTrend]:
        """Analyze trends for each document across dates"""
        print("\n📈 分析趋势...")

        # Group feedback data by document
        doc_groups = defaultdict(list)
        for data in self.feedback_data:
            doc_groups[data.document_name].append(data)

        # Sort each group by date
        for doc_name in doc_groups:
            doc_groups[doc_name].sort(key=lambda x: x.date)

        # Analyze trends
        trends = {}
        single_version_docs = []

        for doc_name, data_list in doc_groups.items():
            if len(data_list) < 2:
                # Handle single version documents
                single_version_docs.append(doc_name)
                single_data = data_list[0]

                # For single version, use baseline comparison
                # Consider accuracy > 70% as good, < 50% as poor
                if single_data.accuracy_rate >= 70:
                    trend_direction = "good_baseline"
                elif single_data.accuracy_rate < 50:
                    trend_direction = "poor_baseline"
                else:
                    trend_direction = "average_baseline"

                # Use accuracy rate as improvement score for single version
                improvement_score = single_data.accuracy_rate - 60  # 60% as baseline

                trends[doc_name] = DocumentTrend(
                    document_name=doc_name,
                    feedback_data=data_list,
                    trend_direction=trend_direction,
                    improvement_score=improvement_score,
                )

                print(
                    f"  📄 {doc_name}: {trend_direction} (单版本, 基线分数: {improvement_score:.1f})"
                )
                continue

            # Calculate improvement score for multi-version documents
            first_data = data_list[0]
            last_data = data_list[-1]

            accuracy_change = last_data.accuracy_rate - first_data.accuracy_rate
            coverage_change = last_data.feedback_coverage - first_data.feedback_coverage

            # Weighted improvement score
            improvement_score = accuracy_change * 0.7 + coverage_change * 0.3

            # Determine trend direction
            if improvement_score > 5:
                trend_direction = "improving"
            elif improvement_score < -5:
                trend_direction = "declining"
            else:
                trend_direction = "stable"

            trends[doc_name] = DocumentTrend(
                document_name=doc_name,
                feedback_data=data_list,
                trend_direction=trend_direction,
                improvement_score=improvement_score,
            )

            print(
                f"  📄 {doc_name}: {trend_direction} (多版本, 改进分数: {improvement_score:.1f})"
            )

        print(f"\n📊 趋势分析总结:")
        print(f"  • 多版本文档: {len(trends) - len(single_version_docs)} 个")
        print(f"  • 单版本文档: {len(single_version_docs)} 个")

        self.document_trends = trends
        return trends

    def generate_date_analysis(self) -> Dict[str, Dict]:
        """Generate analysis by date"""
        print("\n📅 生成日期分析...")

        # Group by date
        date_groups = defaultdict(list)
        for data in self.feedback_data:
            date_groups[data.date].append(data)

        # Analyze each date
        date_analysis = {}
        for date_str, data_list in date_groups.items():
            total_comments = sum(d.total_comments for d in data_list)
            total_feedback = sum(d.comments_with_feedback for d in data_list)
            total_correct = sum(d.correct_modifications for d in data_list)
            total_incorrect = sum(d.incorrect_modifications for d in data_list)

            avg_accuracy = sum(d.accuracy_rate for d in data_list) / len(data_list)
            avg_coverage = sum(d.feedback_coverage for d in data_list) / len(data_list)

            date_analysis[date_str] = {
                "document_count": len(data_list),
                "total_comments": total_comments,
                "total_feedback": total_feedback,
                "total_correct": total_correct,
                "total_incorrect": total_incorrect,
                "average_accuracy": avg_accuracy,
                "average_coverage": avg_coverage,
                "documents": [d.document_name for d in data_list],
            }

            print(
                f"  📅 {date_str}: {len(data_list)} 文档, 平均准确率 {avg_accuracy:.1f}%"
            )

        self.date_analysis = date_analysis
        return date_analysis

    def generate_comprehensive_report(self) -> Dict:
        """Generate comprehensive analysis report"""
        print("\n📋 生成综合报告...")

        # Overall statistics
        total_documents = len(set(d.document_name for d in self.feedback_data))
        total_dates = len(set(d.date for d in self.feedback_data))
        total_records = len(self.feedback_data)

        # Trend statistics
        improving_docs = sum(
            1 for t in self.document_trends.values() if t.trend_direction == "improving"
        )
        declining_docs = sum(
            1 for t in self.document_trends.values() if t.trend_direction == "declining"
        )
        stable_docs = sum(
            1 for t in self.document_trends.values() if t.trend_direction == "stable"
        )

        # Best and worst performers
        best_performer = (
            max(self.document_trends.values(), key=lambda x: x.improvement_score)
            if self.document_trends
            else None
        )
        worst_performer = (
            min(self.document_trends.values(), key=lambda x: x.improvement_score)
            if self.document_trends
            else None
        )

        # Average improvement
        avg_improvement = (
            sum(t.improvement_score for t in self.document_trends.values())
            / len(self.document_trends)
            if self.document_trends
            else 0
        )

        report = {
            "analysis_date": datetime.now().isoformat(),
            "overall_statistics": {
                "total_documents": total_documents,
                "total_dates": total_dates,
                "total_records": total_records,
                "documents_with_trends": len(self.document_trends),
            },
            "trend_statistics": {
                "improving_documents": improving_docs,
                "declining_documents": declining_docs,
                "stable_documents": stable_docs,
                "average_improvement_score": avg_improvement,
                "best_performer": {
                    "document": (
                        best_performer.document_name if best_performer else None
                    ),
                    "score": (
                        best_performer.improvement_score if best_performer else None
                    ),
                },
                "worst_performer": {
                    "document": (
                        worst_performer.document_name if worst_performer else None
                    ),
                    "score": (
                        worst_performer.improvement_score if worst_performer else None
                    ),
                },
            },
            "date_analysis": self.date_analysis,
            "document_trends": {
                name: asdict(trend) for name, trend in self.document_trends.items()
            },
            "raw_data": [asdict(data) for data in self.feedback_data],
        }

        return report

    def print_summary_report(self, report: Dict):
        """Print summary report to console"""
        print("\n" + "=" * 80)
        print("📊 多日期反馈分析报告")
        print("=" * 80)
        print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        # Overall statistics
        overall = report["overall_statistics"]
        print("\n📈 总体统计:")
        print(f"  📄 文档总数: {overall['total_documents']}")
        print(f"  📅 日期总数: {overall['total_dates']}")
        print(f"  📊 记录总数: {overall['total_records']}")
        print(f"  📈 有趋势数据的文档: {overall['documents_with_trends']}")

        # Trend statistics
        trend = report["trend_statistics"]
        print("\n📈 趋势统计:")
        print(f"  ✅ 改进文档: {trend['improving_documents']}")
        print(f"  ❌ 退步文档: {trend['declining_documents']}")
        print(f"  ➖ 稳定文档: {trend['stable_documents']}")
        print(f"  📊 平均改进分数: {trend['average_improvement_score']:.2f}")

        if trend["best_performer"]["document"]:
            print(
                f"  🏆 最佳改进: {trend['best_performer']['document']} ({trend['best_performer']['score']:.2f})"
            )

        if trend["worst_performer"]["document"]:
            print(
                f"  📉 最大退步: {trend['worst_performer']['document']} ({trend['worst_performer']['score']:.2f})"
            )

        # Date analysis
        print("\n📅 日期分析:")
        for date_str, data in sorted(report["date_analysis"].items()):
            print(f"  📅 {date_str}:")
            print(f"    📄 文档数: {data['document_count']}")
            print(f"    💬 总评论数: {data['total_comments']}")
            print(f"    🎯 平均准确率: {data['average_accuracy']:.1f}%")
            print(f"    📊 平均覆盖率: {data['average_coverage']:.1f}%")

        # Document trends
        print("\n📄 文档趋势:")
        for doc_name, trend_data in report["document_trends"].items():
            trend_dir = trend_data["trend_direction"]
            score = trend_data["improvement_score"]

            if trend_dir == "improving":
                status = "🟢 改进中"
            elif trend_dir == "declining":
                status = "🔴 退步中"
            else:
                status = "🟡 稳定"

            print(f"  📄 {doc_name}: {status} ({score:.1f})")

        print("\n" + "=" * 80)

    def export_to_excel(self, report: Dict, output_file: str):
        """Export analysis results to Excel"""
        try:
            wb = Workbook()

            # Summary sheet
            ws_summary = wb.active
            ws_summary.title = "总体统计"

            # Headers
            ws_summary["A1"] = "指标"
            ws_summary["B1"] = "数值"
            ws_summary["A1"].font = Font(bold=True)
            ws_summary["B1"].font = Font(bold=True)

            # Summary data
            overall = report["overall_statistics"]
            trend = report["trend_statistics"]

            row = 2
            summary_data = [
                ("文档总数", overall["total_documents"]),
                ("日期总数", overall["total_dates"]),
                ("记录总数", overall["total_records"]),
                ("有趋势数据的文档", overall["documents_with_trends"]),
                ("", ""),
                ("改进文档", trend["improving_documents"]),
                ("退步文档", trend["declining_documents"]),
                ("稳定文档", trend["stable_documents"]),
                ("平均改进分数", f"{trend['average_improvement_score']:.2f}"),
            ]

            for metric, value in summary_data:
                ws_summary[f"A{row}"] = metric
                ws_summary[f"B{row}"] = value
                row += 1

            # Date analysis sheet
            ws_dates = wb.create_sheet("日期分析")
            date_headers = [
                "日期",
                "文档数",
                "总评论数",
                "总反馈数",
                "平均准确率(%)",
                "平均覆盖率(%)",
            ]

            for col, header in enumerate(date_headers, 1):
                cell = ws_dates.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(
                    start_color="D0E0F0", end_color="D0E0F0", fill_type="solid"
                )

            for row, (date_str, data) in enumerate(
                sorted(report["date_analysis"].items()), 2
            ):
                ws_dates.cell(row=row, column=1, value=date_str)
                ws_dates.cell(row=row, column=2, value=data["document_count"])
                ws_dates.cell(row=row, column=3, value=data["total_comments"])
                ws_dates.cell(row=row, column=4, value=data["total_feedback"])
                ws_dates.cell(
                    row=row, column=5, value=f"{data['average_accuracy']:.1f}"
                )
                ws_dates.cell(
                    row=row, column=6, value=f"{data['average_coverage']:.1f}"
                )

            # Document trends sheet
            ws_trends = wb.create_sheet("文档趋势")
            trend_headers = [
                "文档名称",
                "趋势方向",
                "改进分数",
                "起始准确率(%)",
                "最终准确率(%)",
                "准确率变化",
            ]

            for col, header in enumerate(trend_headers, 1):
                cell = ws_trends.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(
                    start_color="E0F0E0", end_color="E0F0E0", fill_type="solid"
                )

            for row, (doc_name, trend_data) in enumerate(
                report["document_trends"].items(), 2
            ):
                feedback_data = trend_data["feedback_data"]
                start_accuracy = feedback_data[0]["accuracy_rate"]
                end_accuracy = feedback_data[-1]["accuracy_rate"]
                accuracy_change = end_accuracy - start_accuracy

                ws_trends.cell(row=row, column=1, value=doc_name)
                ws_trends.cell(row=row, column=2, value=trend_data["trend_direction"])
                ws_trends.cell(
                    row=row, column=3, value=f"{trend_data['improvement_score']:.2f}"
                )
                ws_trends.cell(row=row, column=4, value=f"{start_accuracy:.1f}")
                ws_trends.cell(row=row, column=5, value=f"{end_accuracy:.1f}")
                ws_trends.cell(row=row, column=6, value=f"{accuracy_change:.1f}")

                # Color code based on trend
                if trend_data["trend_direction"] == "improving":
                    color = "90EE90"  # Light green
                elif trend_data["trend_direction"] == "declining":
                    color = "FFB6C1"  # Light red
                else:
                    color = "FFFFE0"  # Light yellow

                for col in range(1, 7):
                    ws_trends.cell(row=row, column=col).fill = PatternFill(
                        start_color=color, end_color=color, fill_type="solid"
                    )

            # Raw data sheet
            ws_raw = wb.create_sheet("原始数据")
            raw_headers = [
                "日期",
                "文档名称",
                "总评论数",
                "反馈评论数",
                "正确修改",
                "错误修改",
                "准确率(%)",
                "覆盖率(%)",
            ]

            for col, header in enumerate(raw_headers, 1):
                cell = ws_raw.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(
                    start_color="F0F0F0", end_color="F0F0F0", fill_type="solid"
                )

            for row, data in enumerate(report["raw_data"], 2):
                ws_raw.cell(row=row, column=1, value=data["date"])
                ws_raw.cell(row=row, column=2, value=data["document_name"])
                ws_raw.cell(row=row, column=3, value=data["total_comments"])
                ws_raw.cell(row=row, column=4, value=data["comments_with_feedback"])
                ws_raw.cell(row=row, column=5, value=data["correct_modifications"])
                ws_raw.cell(row=row, column=6, value=data["incorrect_modifications"])
                ws_raw.cell(row=row, column=7, value=f"{data['accuracy_rate']:.1f}")
                ws_raw.cell(row=row, column=8, value=f"{data['feedback_coverage']:.1f}")

            # Auto-adjust column widths
            for ws in [ws_summary, ws_dates, ws_trends, ws_raw]:
                for column in ws.columns:
                    max_length = 0
                    column_letter = get_column_letter(column[0].column)
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width

            wb.save(output_file)
            print(f"📊 分析结果已导出到Excel: {output_file}")

        except Exception as e:
            print(f"❌ Excel导出失败: {e}")

    def interactive_summary(self):
        """为 Jupyter notebook 提供交互式摘要"""
        print("📊 反馈分析交互式摘要")
        print("=" * 60)

        # 基本统计
        print(f"📈 数据概览:")
        print(f"  • 文档数量: {len(set(d.document_name for d in self.feedback_data))}")
        print(f"  • 分析日期: {len(set(d.date for d in self.feedback_data))}")
        print(f"  • 总记录数: {len(self.feedback_data)}")

        if self.feedback_data:
            avg_accuracy = sum(d.accuracy_rate for d in self.feedback_data) / len(
                self.feedback_data
            )
            avg_coverage = sum(d.feedback_coverage for d in self.feedback_data) / len(
                self.feedback_data
            )
            total_comments = sum(d.total_comments for d in self.feedback_data)
            total_feedback = sum(d.comments_with_feedback for d in self.feedback_data)

            print(f"  • 平均准确率: {avg_accuracy:.1f}%")
            print(f"  • 平均覆盖率: {avg_coverage:.1f}%")
            print(f"  • 总评论数: {total_comments}")
            print(f"  • 有反馈评论: {total_feedback}")

        # 趋势统计
        if self.document_trends:
            improving = sum(
                1
                for t in self.document_trends.values()
                if t.trend_direction == "improving"
            )
            declining = sum(
                1
                for t in self.document_trends.values()
                if t.trend_direction == "declining"
            )
            stable = sum(
                1
                for t in self.document_trends.values()
                if t.trend_direction == "stable"
            )

            print("\n📈 趋势分析:")
            print(f"  • 改进文档: {improving} 个")
            print(f"  • 退步文档: {declining} 个")
            print(f"  • 稳定文档: {stable} 个")

        # 日期分析
        if self.date_analysis:
            print("\n📅 按日期统计:")
            for date_str, data in sorted(self.date_analysis.items()):
                print(
                    f"  • {date_str}: {data['document_count']} 文档, "
                    f"准确率 {data['average_accuracy']:.1f}%, "
                    f"覆盖率 {data['average_coverage']:.1f}%"
                )

        print("=" * 60)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        formatter_class=argparse.RawDescriptionHelpFormatter,
        description="多日期反馈统计分析工具",
        epilog="""
使用示例:
    python multi_version_analyzer.py feedback_results/                    # 分析feedback_results目录
    python multi_version_analyzer.py feedback_results/ --output-excel analysis.xlsx  # 导出到Excel
    python multi_version_analyzer.py feedback_results/ --output-json analysis.json   # 导出到JSON


功能特点:
    - 自动识别日期目录（YYYY-MM-DD）或小时目录（YYYY-MM-DD_HH）和Excel分析文件
    - 分析不同时间点间的反馈变化趋势
    - 计算文档改进分数和整体统计
    - 支持Excel、JSON导出
        """,
    )

    parser.add_argument("feedback_directory", help="包含日期目录的反馈数据目录路径")
    parser.add_argument("--output-excel", help="Excel输出文件路径")
    parser.add_argument("--output-json", help="JSON输出文件路径")

    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出模式")

    args = parser.parse_args()

    try:
        print("🚀 启动多日期反馈分析器...")

        # Initialize analyzer
        analyzer = MultiFeedbackAnalyzer(args.feedback_directory)

        # Scan feedback files
        analyzer.scan_feedback_files()

        # Analyze trends
        analyzer.analyze_trends()

        # Generate date analysis
        analyzer.generate_date_analysis()

        # Generate comprehensive report
        report = analyzer.generate_comprehensive_report()

        # Print summary
        analyzer.print_summary_report(report)

        # Export results
        if args.output_excel:
            analyzer.export_to_excel(report, args.output_excel)

        if args.output_json:
            with open(args.output_json, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"📄 分析结果已导出到JSON: {args.output_json}")

        print("\n✅ 分析完成!")

    except Exception as e:
        print(f"❌ 错误: {str(e)}", file=sys.stderr)
        if args.verbose:
            import traceback

            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
