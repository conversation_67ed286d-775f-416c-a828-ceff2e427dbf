#!/bin/bash

# Batch processing script for feedback documents
# This script processes all .docx files in the feedback directory using feedback_collector.py

# Set script directory and paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
FEEDBACK_DIR="$SCRIPT_DIR/feedback"
COLLECTOR_SCRIPT="$SCRIPT_DIR/feedback_collector.py"
# Get current date and hour for directory organization
CURRENT_DATETIME=$(date +"%Y-%m-%d_%H")
OUTPUT_DIR="$SCRIPT_DIR/feedback_results/$CURRENT_DATETIME"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
  echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
  echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Check if feedback directory exists
if [ ! -d "$FEEDBACK_DIR" ]; then
  print_error "Feedback directory not found: $FEEDBACK_DIR"
  exit 1
fi

# Check if collector script exists
if [ ! -f "$COLLECTOR_SCRIPT" ]; then
  print_error "Feedback collector script not found: $COLLECTOR_SCRIPT"
  exit 1
fi

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Count total files
total_files=$(find "$FEEDBACK_DIR" -name "*.docx" -type f | wc -l)

if [ "$total_files" -eq 0 ]; then
  print_warning "No .docx files found in $FEEDBACK_DIR"
  exit 0
fi

print_status "Found $total_files .docx files to process"
print_status "Output directory: $OUTPUT_DIR"
echo

# Initialize counters
processed=0
successful=0
failed=0

# Process each .docx file
find "$FEEDBACK_DIR" -name "*.docx" -type f | while read -r file; do
  processed=$((processed + 1))
  filename=$(basename "$file")
  base_name="${filename%.*}"

  print_status "Processing file $processed/$total_files: $filename"

  # Generate output filenames
  excel_output="$OUTPUT_DIR/${base_name}_analysis.xlsx"
  word_copy="$OUTPUT_DIR/$filename"

  # Run the feedback collector with Excel output only
  echo "  → Running analysis..."

  # Run with Excel output
  if python3 "$COLLECTOR_SCRIPT" "$file" --output-format excel --output-file "$excel_output" 2>/dev/null; then
    print_success "  ✓ Excel analysis completed: $(basename "$excel_output")"

    # Copy original Word file to results directory
    if cp "$file" "$word_copy"; then
      print_success "  ✓ Original file copied: $(basename "$word_copy")"
      successful=$((successful + 1))
    else
      print_warning "  ⚠ Failed to copy original file"
    fi
  else
    print_error "  ✗ Analysis failed for $filename"
    failed=$((failed + 1))

    # Try to run with verbose output to see the error
    echo "  → Attempting verbose analysis to show error details..."
    python3 "$COLLECTOR_SCRIPT" "$file" --verbose 2>&1 | head -10 | sed 's/^/    /'
  fi

  echo
done

# Final summary
echo "=" * 60
print_status "Batch processing completed!"
echo
print_status "Summary:"
echo "  Total files found: $total_files"
echo "  Successfully processed: $successful"
echo "  Failed: $failed"
echo
print_status "Results saved in: $OUTPUT_DIR"

# List generated files
if [ -d "$OUTPUT_DIR" ] && [ "$(ls -A "$OUTPUT_DIR" 2>/dev/null)" ]; then
  echo
  print_status "Generated files:"
  echo "  Excel files:"
  ls -la "$OUTPUT_DIR" | grep '\.xlsx$' | awk '{print "    " $9 " (" $5 " bytes)"}'
  echo "  Original Word files:"
  ls -la "$OUTPUT_DIR" | grep '\.docx$' | awk '{print "    " $9 " (" $5 " bytes)"}'
else
  print_warning "No output files were generated"
fi
