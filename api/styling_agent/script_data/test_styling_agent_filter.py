#!/usr/bin/env python3
"""
Test script to verify styling_agent comment filtering functionality
"""

import sys
import tempfile
from pathlib import Path

from feedback_analyzer import CommentReply, EnhancedCommentInfo, <PERSON>edbackAnaly<PERSON>


def create_test_comments():
    """Create test comments for verification"""
    comments = {
        "1": EnhancedCommentInfo(
            comment_id="1",
            author="styling_agent",
            text="AI suggestion: Consider revising this sentence for clarity.",
            date="2024-01-01T10:00:00Z",
        ),
        "2": EnhancedCommentInfo(
            comment_id="2",
            author="user123",
            text="This looks good to me.",
            date="2024-01-01T10:05:00Z",
        ),
        "3": EnhancedCommentInfo(
            comment_id="3",
            author="styling_agent",
            text="AI suggestion: Grammar correction needed here.",
            date="2024-01-01T10:10:00Z",
        ),
        "4": EnhancedCommentInfo(
            comment_id="4",
            author="reviewer",
            text="Please check this reference.",
            date="2024-01-01T10:15:00Z",
        ),
    }

    # Add some replies to styling_agent comments
    comments["1"].replies.append(
        CommentReply(
            reply_id="r1",
            author="user123",
            text="t",  # positive feedback
            date="2024-01-01T10:02:00Z",
        )
    )

    comments["3"].replies.append(
        CommentReply(
            reply_id="r2",
            author="user123",
            text="f",  # negative feedback
            date="2024-01-01T10:12:00Z",
        )
    )

    return comments


def test_styling_agent_filter():
    """Test the styling_agent filtering functionality"""
    print("Testing styling_agent comment filtering...")

    # Create a mock analyzer with styling_agent filter
    class MockAnalyzer(FeedbackAnalyzer):
        def __init__(self, filter_author=None):
            # Skip the parent __init__ to avoid file operations
            self.filter_author = filter_author
            self.comments = []

        def test_process_comments(self, test_comments):
            """Test the comment processing logic"""
            return self._process_enhanced_comments(test_comments)

    # Test with styling_agent filter
    analyzer_with_filter = MockAnalyzer(filter_author="styling_agent")
    test_comments = create_test_comments()

    filtered_comments = analyzer_with_filter.test_process_comments(test_comments)

    print(f"Total test comments: {len(test_comments)}")
    print(f"Filtered comments (styling_agent only): {len(filtered_comments)}")

    # Verify results
    styling_agent_count = sum(
        1 for c in test_comments.values() if c.author == "styling_agent"
    )
    assert (
        len(filtered_comments) == styling_agent_count
    ), f"Expected {styling_agent_count} styling_agent comments, got {len(filtered_comments)}"

    # Verify all filtered comments are from styling_agent
    for comment in filtered_comments:
        assert (
            comment.author == "styling_agent"
        ), f"Found non-styling_agent comment: {comment.author}"
        print(
            f"✓ Comment {comment.comment_id}: {comment.author} - {comment.text[:50]}..."
        )

        # Check if feedback was processed
        if comment.user_feedback:
            print(
                f"  └─ User feedback: {comment.user_feedback} (Correct: {comment.is_correct})"
            )

    # Test without filter (should include AI comments)
    analyzer_no_filter = MockAnalyzer(filter_author=None)
    all_ai_comments = analyzer_no_filter.test_process_comments(test_comments)

    print(f"\nWithout filter (AI comments): {len(all_ai_comments)}")
    for comment in all_ai_comments:
        print(
            f"✓ Comment {comment.comment_id}: {comment.author} - {comment.text[:50]}..."
        )

    print("\n✅ All tests passed! styling_agent filtering is working correctly.")


if __name__ == "__main__":
    test_styling_agent_filter()
