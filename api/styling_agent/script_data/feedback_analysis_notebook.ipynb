# 导入必要的库
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 强制清除matplotlib字体缓存并重新配置
import matplotlib  # noqa: E402

# 删除matplotlib字体缓存文件
import shutil  # noqa: E402
cache_dir = matplotlib.get_cachedir()
try:
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir)
        print(f"✅ 已删除matplotlib缓存目录: {cache_dir}")
except Exception as e:
    print(f"⚠️ 删除缓存目录失败: {e}")

# 设置中文字体 - 使用检测到的字体
matplotlib.rcParams['font.family'] = ['sans-serif']
# 基于你的系统，优先使用Noto Sans CJK
matplotlib.rcParams['font.sans-serif'] = [
    'Noto Sans CJK SC',  # 简体中文
    'Noto Sans CJK JP', 
    'WenQuanYi Micro Hei',
    'DejaVu Sans'
]
matplotlib.rcParams['axes.unicode_minus'] = False

# 强制设置字体
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = matplotlib.rcParams['font.sans-serif']

# 设置图表样式
sns.set_style("whitegrid")
plt.rcParams['figure.figsize'] = (12, 8)

# 导入我们的分析器
from multi_version_analyzer import MultiFeedbackAnalyzer  # noqa: E402

print("✅ 环境设置完成")
print(f"当前字体设置: {matplotlib.rcParams['font.sans-serif'][:3]}")

# # 直接指定字体进行测试
# def test_specific_font():
#     """直接指定字体进行测试"""
#     import matplotlib.pyplot as plt
#     import matplotlib.font_manager as fm
    
#     # 尝试几种不同的字体设置方法
#     fonts_to_try = [
#         'Noto Sans CJK SC',
#         'Noto Sans CJK JP', 
#         'WenQuanYi Micro Hei',
#         'DejaVu Sans'
#     ]
    
#     fig, axes = plt.subplots(2, 2, figsize=(12, 8))
#     axes = axes.flatten()
    
#     for i, font_name in enumerate(fonts_to_try):
#         ax = axes[i]
        
#         # 测试数据
#         categories = ['数据分析', '可视化', '趋势', '质量']
#         values = [85, 72, 68, 91]
        
#         # 尝试使用特定字体
#         try:
#             bars = ax.bar(categories, values, color=['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'])
#             ax.set_title(f'字体测试: {font_name}', fontproperties=fm.FontProperties(family=font_name))
            
#             # 设置标签字体
#             for label in ax.get_xticklabels():
#                 label.set_fontproperties(fm.FontProperties(family=font_name))
            
#             ax.set_ylabel('值', fontproperties=fm.FontProperties(family=font_name))
            
#         except Exception as e:
#             ax.text(0.5, 0.5, f'字体 {font_name} 失败', ha='center', va='center', transform=ax.transAxes)
#             ax.set_title(f'字体 {font_name} - 错误')
    
#     plt.tight_layout()
#     plt.show()
    
#     # 显示实际可用的CJK字体
#     print("\n🔍 系统中可用的CJK字体:")
#     all_fonts = [f.name for f in fm.fontManager.ttflist]
#     cjk_fonts = [f for f in all_fonts if 'CJK' in f or 'WenQuanYi' in f or 'Noto' in f]
#     for font in sorted(set(cjk_fonts))[:10]:
#         print(f"  - {font}")

# # 运行字体测试
# test_specific_font()

# 创建一个全局字体设置函数
def set_chinese_font_globally():
    """设置全局中文字体"""
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    
    # 找到系统中的中文字体
    chinese_font = None
    font_candidates = [
        'Noto Sans CJK SC',
        'Noto Sans CJK JP',
        'WenQuanYi Micro Hei',
        'Microsoft YaHei',
        'SimHei',
        'Arial Unicode MS'
    ]
    
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    for font_name in font_candidates:
        if font_name in available_fonts:
            chinese_font = font_name
            break
    
    if chinese_font:
        print(f"✅ 找到可用中文字体: {chinese_font}")
        # 设置全局字体
        plt.rcParams['font.family'] = chinese_font
        plt.rcParams['axes.unicode_minus'] = False
        return chinese_font
    else:
        print("❌ 未找到可用中文字体")
        return None

set_chinese_font_globally()

# # 创建一个中文友好的绘图函数
# def plot_with_chinese(title, xlabel, ylabel, categories, values, ax=None, **kwargs):
#     """使用中文字体绘制图表"""
#     import matplotlib.pyplot as plt
#     import matplotlib.font_manager as fm
    
#     if ax is None:
#         fig, ax = plt.subplots(figsize=(10, 6))
    
#     # 使用指定的中文字体
#     chinese_font = set_chinese_font_globally()
    
#     if chinese_font:
#         font_prop = fm.FontProperties(family=chinese_font)
        
#         # 绘制图表
#         bars = ax.bar(categories, values, **kwargs)
        
#         # 设置标题和标签
#         ax.set_title(title, fontproperties=font_prop, fontsize=14, fontweight='bold')
#         ax.set_xlabel(xlabel, fontproperties=font_prop, fontsize=12)
#         ax.set_ylabel(ylabel, fontproperties=font_prop, fontsize=12)
        
#         # 设置刻度标签
#         ax.set_xticklabels(categories, fontproperties=font_prop)
        
#         # 在柱子上添加数值
#         for bar, value in zip(bars, values):
#             height = bar.get_height()
#             ax.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
#                     f'{value}', ha='center', va='bottom', fontproperties=font_prop)
#     else:
#         # 如果没有中文字体，使用英文
#         bars = ax.bar(categories, values, **kwargs)
#         ax.set_title(title.encode('ascii', 'ignore').decode('ascii') or 'Chart')
#         ax.set_xlabel(xlabel.encode('ascii', 'ignore').decode('ascii') or 'X')
#         ax.set_ylabel(ylabel.encode('ascii', 'ignore').decode('ascii') or 'Y')
        
#         print("⚠️ 使用默认字体，中文可能显示异常")
    
#     ax.grid(True, alpha=0.3)
#     plt.tight_layout()
    
#     if ax is None:
#         plt.show()
    
#     return ax

# # 测试中文友好的绘图函数
# print("🎨 测试中文友好的绘图函数:")
# categories = ['文档分析', '数据可视化', '趋势分析', '质量评估', '用户反馈']
# values = [85, 72, 68, 91, 76]
# colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#ffeaa7']

# plot_with_chinese('用户反馈分析结果', '分析类型', '准确率 (%)', categories, values, color=colors)

# print("✅ 中文字体配置完成！后续图表将使用此配置。")

# 配置数据路径
feedback_directory = "./feedback_results/"  # 修改为你的反馈数据目录

# 初始化分析器
analyzer = MultiFeedbackAnalyzer(feedback_directory)

# 加载数据
print("🔄 开始数据加载...")
analyzer.scan_feedback_files()
analyzer.analyze_trends()
analyzer.generate_date_analysis()
report = analyzer.generate_comprehensive_report()

print("\n✅ 数据加载完成！")
print("📊 数据概览:")
print(f"  - 文档总数: {report['overall_statistics']['total_documents']}")
print(f"  - 日期总数: {report['overall_statistics']['total_dates']}")
print(f"  - 记录总数: {report['overall_statistics']['total_records']}")

# 创建自定义日期解析函数
def parse_datetime_string(date_str):
    """
    解析日期字符串，支持两种格式：
    - YYYY-MM-DD (日期级)
    - YYYY-MM-DD_HH (小时级)
    """
    if '_' in date_str:
        # 小时级格式：YYYY-MM-DD_HH
        return pd.to_datetime(date_str.replace('_', ' ') + ':00:00')
    else:
        # 日期级格式：YYYY-MM-DD
        return pd.to_datetime(date_str)

# 创建 DataFrame 便于分析
df_raw = pd.DataFrame(report['raw_data'])
df_raw['date'] = df_raw['date'].apply(parse_datetime_string)

# 显示基本统计信息
print("📊 原始数据统计:")
display(df_raw.describe())

print("\n📅 按时间点分组的数据:")
display(df_raw.groupby('date').agg({
    'total_comments': 'sum',
    'comments_with_feedback': 'sum',
    'correct_modifications': 'sum',
    'incorrect_modifications': 'sum',
    'accuracy_rate': 'mean',
    'feedback_coverage': 'mean'
}).round(2))

# 计算三种准确率
def calculate_accuracy_metrics(data):
    """
    计算三种准确率指标
    """
    total_comments = data['total_comments']
    comments_with_feedback = data['comments_with_feedback']
    correct_modifications = data['correct_modifications']
    comments_without_feedback = total_comments - comments_with_feedback
    
    # 1. Feedback Accuracy - 只考虑有反馈的评论
    feedback_accuracy = (
        (correct_modifications / comments_with_feedback * 100)
        if comments_with_feedback > 0 else 0
    )
    
    # 2. Overall Accuracy - 保守估计（无反馈=错误）
    overall_accuracy = (
        (correct_modifications / total_comments * 100)
        if total_comments > 0 else 0
    )
    
    # 3. Optimistic Accuracy - 乐观估计（无反馈=正确）
    optimistic_accuracy = (
        ((correct_modifications + comments_without_feedback) / total_comments * 100)
        if total_comments > 0 else 0
    )
    
    return {
        'feedback_accuracy': feedback_accuracy,
        'overall_accuracy': overall_accuracy,
        'optimistic_accuracy': optimistic_accuracy,
        'comments_with_feedback': comments_with_feedback,
        'comments_without_feedback': comments_without_feedback,
        'total_comments': total_comments,
        'correct_modifications': correct_modifications
    }

# 为每个数据点计算三种准确率
accuracy_data = []
for item in report['raw_data']:
    metrics = calculate_accuracy_metrics(item)
    metrics.update({
        'date': item['date'],
        'document_name': item['document_name']
    })
    accuracy_data.append(metrics)

df_accuracy = pd.DataFrame(accuracy_data)
df_accuracy['date'] = df_accuracy['date'].apply(parse_datetime_string)

print("📊 三种准确率对比:")
display(df_accuracy[['date', 'document_name', 'feedback_accuracy', 'overall_accuracy', 'optimistic_accuracy']].round(2))

# 创建三种准确率的可视化对比
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('三种准确率对比分析', fontsize=16, fontweight='bold')

# 1. 三种准确率的柱状图对比
ax1 = axes[0, 0]
accuracy_means = {
    'Feedback Accuracy\n(仅有反馈)': df_accuracy['feedback_accuracy'].mean(),
    'Overall Accuracy\n(保守估计)': df_accuracy['overall_accuracy'].mean(),
    'Optimistic Accuracy\n(乐观估计)': df_accuracy['optimistic_accuracy'].mean()
}

colors = ['#FF6B6B', '#4ECDC4', '#45B7D1']
bars = ax1.bar(accuracy_means.keys(), accuracy_means.values(), color=colors, alpha=0.8)
ax1.set_ylabel('准确率 (%)')
ax1.set_title('三种准确率平均值对比')
ax1.set_ylim(0, 100)

# 在柱状图上添加数值标签
for bar, value in zip(bars, accuracy_means.values()):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
             f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')

# 2. 时间序列对比
ax2 = axes[0, 1]
grouped = df_accuracy.groupby('date').agg({
    'feedback_accuracy': 'mean',
    'overall_accuracy': 'mean',
    'optimistic_accuracy': 'mean'
}).reset_index()

ax2.plot(grouped['date'], grouped['feedback_accuracy'], 
         marker='o', linewidth=2, label='Feedback Accuracy', color=colors[0])
ax2.plot(grouped['date'], grouped['overall_accuracy'], 
         marker='s', linewidth=2, label='Overall Accuracy', color=colors[1])
ax2.plot(grouped['date'], grouped['optimistic_accuracy'], 
         marker='^', linewidth=2, label='Optimistic Accuracy', color=colors[2])

ax2.set_xlabel('日期')
ax2.set_ylabel('准确率 (%)')
ax2.set_title('三种准确率时间趋势')
ax2.legend()
ax2.grid(True, alpha=0.3)
ax2.tick_params(axis='x', rotation=45)

# 3. 反馈覆盖率与准确率的关系
ax3 = axes[1, 0]
df_accuracy['feedback_coverage'] = (df_accuracy['comments_with_feedback'] / df_accuracy['total_comments'] * 100)

scatter = ax3.scatter(df_accuracy['feedback_coverage'], df_accuracy['feedback_accuracy'], 
                     c=df_accuracy['total_comments'], cmap='viridis', alpha=0.7, s=100)
ax3.set_xlabel('反馈覆盖率 (%)')
ax3.set_ylabel('Feedback Accuracy (%)')
ax3.set_title('反馈覆盖率 vs Feedback Accuracy')
plt.colorbar(scatter, ax=ax3, label='总评论数')

# 4. 准确率分布饼图
ax4 = axes[1, 1]
total_comments_sum = df_accuracy['total_comments'].sum()
correct_sum = df_accuracy['correct_modifications'].sum()
feedback_sum = df_accuracy['comments_with_feedback'].sum()
no_feedback_sum = total_comments_sum - feedback_sum
incorrect_sum = feedback_sum - correct_sum

sizes = [correct_sum, incorrect_sum, no_feedback_sum]
labels = ['正确修改', '错误修改', '无反馈']
colors_pie = ['#2ECC71', '#E74C3C', '#95A5A6']
explode = (0.05, 0.05, 0.05)

wedges, texts, autotexts = ax4.pie(sizes, labels=labels, colors=colors_pie, 
                                  autopct='%1.1f%%', explode=explode, startangle=90)
ax4.set_title('评论分布情况')

plt.tight_layout()
plt.show()

# 详细的准确率分析表格
print("📋 详细准确率分析:")
print("=" * 80)

# 计算总体统计
total_stats = {
    'total_comments': df_accuracy['total_comments'].sum(),
    'comments_with_feedback': df_accuracy['comments_with_feedback'].sum(),
    'correct_modifications': df_accuracy['correct_modifications'].sum()
}

overall_metrics = calculate_accuracy_metrics(total_stats)

print("📊 总体数据统计:")
print(f"  • 总评论数: {total_stats['total_comments']}")
print(f"  • 有反馈评论数: {total_stats['comments_with_feedback']}")
print(f"  • 正确修改数: {total_stats['correct_modifications']}")
print(f"  • 无反馈评论数: {total_stats['total_comments'] - total_stats['comments_with_feedback']}")
print()

print("🎯 三种准确率对比:")
print(f"  1. Feedback Accuracy (仅有反馈): {overall_metrics['feedback_accuracy']:.2f}%")
print("     计算方式: 正确修改数 ÷ 有反馈评论数")
print("     说明: 只考虑用户明确给出反馈的评论，最客观的指标")
print()

print(f"  2. Overall Accuracy (保守估计): {overall_metrics['overall_accuracy']:.2f}%")
print("     计算方式: 正确修改数 ÷ 总评论数")
print("     说明: 将无反馈评论视为错误，最保守的估计")
print()

print(f"  3. Optimistic Accuracy (乐观估计): {overall_metrics['optimistic_accuracy']:.2f}%")
print("     计算方式: (正确修改数 + 无反馈评论数) ÷ 总评论数")
print("     说明: 将无反馈评论视为正确，最乐观的估计")
print()

print(f"💡 建议使用 Feedback Accuracy ({overall_metrics['feedback_accuracy']:.2f}%) 作为主要评估指标")
print("   因为它基于真实的用户反馈，避免了对无反馈评论的主观假设")

# 总体趋势分析
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('📈 用户反馈总体趋势分析', fontsize=16, fontweight='bold')

# 按日期汇总数据
date_summary = df_raw.groupby('date').agg({
    'total_comments': 'sum',
    'comments_with_feedback': 'sum',
    'accuracy_rate': 'mean',
    'feedback_coverage': 'mean',
    'document_name': 'count'
}).rename(columns={'document_name': 'document_count'})

# 1. 每日文档数和评论数
ax1 = axes[0, 0]
ax1_twin = ax1.twinx()

bars = ax1.bar(date_summary.index, date_summary['document_count'], 
               alpha=0.7, color='skyblue', label='文档数')
line = ax1_twin.plot(date_summary.index, date_summary['total_comments'], 
                     color='red', marker='o', linewidth=2, label='总评论数')

ax1.set_xlabel('日期')
ax1.set_ylabel('文档数量', color='blue')
ax1_twin.set_ylabel('评论数量', color='red')
ax1.set_title('每日文档数量与评论数量')
ax1.tick_params(axis='x', rotation=45)

# 2. 准确率趋势
ax2 = axes[0, 1]
ax2.plot(date_summary.index, date_summary['accuracy_rate'], 
         marker='o', linewidth=3, markersize=8, color='green')
ax2.set_xlabel('日期')
ax2.set_ylabel('准确率 (%)')
ax2.set_title('平均准确率趋势')
ax2.tick_params(axis='x', rotation=45)
ax2.grid(True, alpha=0.3)

# 3. 反馈覆盖率趋势
ax3 = axes[1, 0]
ax3.plot(date_summary.index, date_summary['feedback_coverage'], 
         marker='s', linewidth=3, markersize=8, color='orange')
ax3.set_xlabel('日期')
ax3.set_ylabel('覆盖率 (%)')
ax3.set_title('反馈覆盖率趋势')
ax3.tick_params(axis='x', rotation=45)
ax3.grid(True, alpha=0.3)

# 4. 反馈质量分布
ax4 = axes[1, 1]
feedback_quality = date_summary[['comments_with_feedback', 'total_comments']].copy()
feedback_quality['without_feedback'] = feedback_quality['total_comments'] - feedback_quality['comments_with_feedback']

bottom = np.zeros(len(feedback_quality))
ax4.bar(feedback_quality.index, feedback_quality['comments_with_feedback'], 
        label='有反馈', color='lightgreen')
ax4.bar(feedback_quality.index, feedback_quality['without_feedback'], 
        bottom=feedback_quality['comments_with_feedback'], 
        label='无反馈', color='lightcoral')

ax4.set_xlabel('日期')
ax4.set_ylabel('评论数量')
ax4.set_title('反馈评论分布')
ax4.legend()
ax4.tick_params(axis='x', rotation=45)

plt.tight_layout()
plt.show()

# 文档改进趋势分析
if report['document_trends']:
    # 创建文档趋势 DataFrame
    trend_data = []
    for doc_name, trend in report['document_trends'].items():
        for data in trend['feedback_data']:
            trend_data.append({
                'document_name': doc_name,
                'date': data['date'],
                'accuracy_rate': data['accuracy_rate'],
                'feedback_coverage': data['feedback_coverage'],
                'total_comments': data['total_comments'],
                'trend_direction': trend['trend_direction'],
                'improvement_score': trend['improvement_score']
            })
    
    df_trends = pd.DataFrame(trend_data)
    df_trends['date'] = df_trends['date'].apply(parse_datetime_string)
    
    # 分离多版本和单版本文档
    multi_version_docs = df_trends.groupby('document_name').size() > 1
    multi_version_names = multi_version_docs[multi_version_docs].index.tolist()
    single_version_names = multi_version_docs[~multi_version_docs].index.tolist()
    
    print(f"📊 文档分类统计:")
    print(f"  • 多版本文档: {len(multi_version_names)} 个")
    print(f"  • 单版本文档: {len(single_version_names)} 个")
    print(f"  • 总文档数: {len(report['document_trends'])} 个")
    
    # 1. 文档改进分数排名（包含所有文档）
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    fig.suptitle('📄 文档级别分析（包含单版本和多版本文档）', fontsize=16, fontweight='bold')
    
    # 改进分数排名
    ax1 = axes[0, 0]
    improvement_scores = df_trends.groupby('document_name')['improvement_score'].first().sort_values(ascending=True)
    
    # 为不同类型的文档设置不同颜色
    def get_color(doc_name, score):
        if doc_name in single_version_names:
            # 单版本文档用蓝色系
            if score > 10: return 'darkblue'
            elif score > 0: return 'steelblue'
            elif score > -10: return 'lightblue'
            else: return 'navy'
        else:
            # 多版本文档用传统颜色
            if score > 5: return 'green'
            elif score > 0: return 'lightgreen'
            elif score > -5: return 'orange'
            else: return 'red'
    
    colors = [get_color(name, score) for name, score in improvement_scores.items()]
    
    bars = ax1.barh(range(len(improvement_scores)), improvement_scores.values, color=colors, alpha=0.7)
    ax1.set_yticks(range(len(improvement_scores)))
    
    # 添加文档类型标识
    labels = []
    for name in improvement_scores.index:
        short_name = name[:25] + '...' if len(name) > 25 else name
        doc_type = '(单)' if name in single_version_names else '(多)'
        labels.append(f"{short_name} {doc_type}")
    
    ax1.set_yticklabels(labels)
    ax1.set_xlabel('改进分数')
    ax1.set_title('文档改进分数排名\n蓝色=单版本文档，其他=多版本文档')
    ax1.axvline(x=0, color='black', linestyle='--', alpha=0.5)
    
    # 添加数值标签
    for i, (bar, score) in enumerate(zip(bars, improvement_scores.values)):
        ax1.text(score + (1 if score >= 0 else -1), i, f'{score:.1f}', 
                va='center', ha='left' if score >= 0 else 'right', fontsize=8)
    
    # 2. 准确率趋势对比（仅多版本文档）
    ax2 = axes[0, 1]
    if multi_version_names:
        for doc_name in multi_version_names:
            doc_data = df_trends[df_trends['document_name'] == doc_name].sort_values('date')
            ax2.plot(doc_data['date'], doc_data['accuracy_rate'], 
                    marker='o', label=doc_name[:15] + '...' if len(doc_name) > 15 else doc_name, linewidth=2)
        
        ax2.set_xlabel('日期')
        ax2.set_ylabel('准确率 (%)')
        ax2.set_title('多版本文档准确率趋势')
        ax2.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        ax2.tick_params(axis='x', rotation=45)
        ax2.grid(True, alpha=0.3)
    else:
        ax2.text(0.5, 0.5, '没有多版本文档数据', ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('多版本文档准确率趋势')
    
    # 3. 评论数与准确率散点图（区分文档类型）
    ax3 = axes[1, 0]
    
    # 分别绘制单版本和多版本文档
    if single_version_names:
        single_data = df_trends[df_trends['document_name'].isin(single_version_names)]
        scatter1 = ax3.scatter(single_data['total_comments'], single_data['accuracy_rate'], 
                             c='blue', alpha=0.7, s=100, marker='s', label='单版本文档')
    
    if multi_version_names:
        multi_data = df_trends[df_trends['document_name'].isin(multi_version_names)]
        scatter2 = ax3.scatter(multi_data['total_comments'], multi_data['accuracy_rate'], 
                             c=multi_data['improvement_score'], cmap='RdYlGn', 
                             alpha=0.7, s=100, marker='o', label='多版本文档')
        plt.colorbar(scatter2, ax=ax3, label='改进分数')
    
    ax3.set_xlabel('总评论数')
    ax3.set_ylabel('准确率 (%)')
    ax3.set_title('评论数 vs 准确率\n方形=单版本，圆形=多版本')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 趋势方向分布
    ax4 = axes[1, 1]
    trend_counts = df_trends.groupby('trend_direction')['document_name'].nunique()
    
    # 更新颜色映射以包含单版本文档类型
    colors_pie = {
        'improving': 'lightgreen', 
        'stable': 'lightyellow', 
        'declining': 'lightcoral',
        'good_baseline': 'darkblue',
        'average_baseline': 'steelblue',
        'poor_baseline': 'lightblue'
    }
    
    # 中文标签映射
    label_mapping = {
        'improving': '改进中',
        'stable': '稳定',
        'declining': '下降',
        'good_baseline': '单版本-优秀',
        'average_baseline': '单版本-一般',
        'poor_baseline': '单版本-较差'
    }
    
    pie_colors = [colors_pie.get(trend, 'gray') for trend in trend_counts.index]
    pie_labels = [label_mapping.get(trend, trend) for trend in trend_counts.index]
    
    wedges, texts, autotexts = ax4.pie(trend_counts.values, labels=pie_labels, 
                                      autopct='%1.1f%%', colors=pie_colors)
    ax4.set_title('文档趋势方向分布')
    
    plt.tight_layout()
    plt.show()
    
    # 显示详细趋势表格
    print("\n📋 文档趋势详细表格:")
    trend_summary = df_trends.groupby('document_name').agg({
        'trend_direction': 'first',
        'improvement_score': 'first',
        'accuracy_rate': ['first', 'last'],
        'total_comments': 'mean'
    }).round(2)
    
    # 重命名列
    trend_summary.columns = ['趋势方向', '改进分数', '初始准确率', '最终准确率', '平均评论数']
    
    # 为单版本文档调整显示
    for idx in trend_summary.index:
        if idx in single_version_names:
            trend_summary.loc[idx, '最终准确率'] = trend_summary.loc[idx, '初始准确率']
    
    trend_summary['准确率变化'] = trend_summary['最终准确率'] - trend_summary['初始准确率']
    trend_summary['文档类型'] = ['单版本' if name in single_version_names else '多版本' for name in trend_summary.index]
    
    # 中文趋势方向
    trend_summary['趋势方向'] = trend_summary['趋势方向'].map(label_mapping)
    
    # 按改进分数排序
    trend_summary = trend_summary.sort_values('改进分数', ascending=False)
    
    display(trend_summary)
    
    # 额外的统计信息
    print("\n📈 详细统计:")
    if single_version_names:
        single_stats = df_trends[df_trends['document_name'].isin(single_version_names)]
        print(f"单版本文档平均准确率: {single_stats['accuracy_rate'].mean():.1f}%")
    
    if multi_version_names:
        multi_stats = df_trends[df_trends['document_name'].isin(multi_version_names)]
        print(f"多版本文档平均准确率: {multi_stats['accuracy_rate'].mean():.1f}%")
        print(f"多版本文档平均改进分数: {multi_stats.groupby('document_name')['improvement_score'].first().mean():.1f}")
    
else:
    print("⚠️ 没有文档趋势数据")

# 数据导出功能
print("💾 数据导出选项:")

# 导出到 Excel
excel_output = "feedback_analysis_results.xlsx"
analyzer.export_to_excel(report, excel_output)

# # 导出到 JSON
# json_output = "feedback_analysis_results.json"
# import json
# with open(json_output, 'w', encoding='utf-8') as f:
#     json.dump(report, f, indent=2, ensure_ascii=False)
# print(f"📄 分析结果已导出到JSON: {json_output}")

# # 导出 CSV 文件用于进一步分析
# csv_output = "feedback_analysis_raw_data.csv"
# df_raw.to_csv(csv_output, index=False, encoding='utf-8-sig')
# print(f"📊 原始数据已导出到CSV: {csv_output}")

!jupyter nbconvert --to html feedback_analysis_notebook.ipynb --no-input --output feedback_analysis_report_no_code.html

# 生成总结报告
print("📋 分析总结报告")
print("=" * 80)

overall = report['overall_statistics']
trend = report['trend_statistics']

print("\n📈 关键指标:")
print(f"  • 总文档数: {overall['total_documents']}")
print(f"  • 分析日期数: {overall['total_dates']}")
print(f"  • 总记录数: {overall['total_records']}")
print(f"  • 平均准确率: {df_raw['accuracy_rate'].mean():.1f}%")
print(f"  • 平均覆盖率: {df_raw['feedback_coverage'].mean():.1f}%")
print(f"  • 总评论数: {df_raw['total_comments'].sum()}")
print(f"  • 有反馈评论数: {df_raw['comments_with_feedback'].sum()}")

if trend['improving_documents'] > 0 or trend['declining_documents'] > 0:
    print("\n📊 趋势分析:")
    print(f"  • 改进文档: {trend['improving_documents']} 个")
    print(f"  • 退步文档: {trend['declining_documents']} 个")
    print(f"  • 稳定文档: {trend['stable_documents']} 个")
    print(f"  • 平均改进分数: {trend['average_improvement_score']:.2f}")
    
    if trend['best_performer']['document']:
        print(f"  • 最佳改进: {trend['best_performer']['document'][:30]}... ({trend['best_performer']['score']:.2f})")
    
    if trend['worst_performer']['document']:
        print(f"  • 最大退步: {trend['worst_performer']['document'][:30]}... ({trend['worst_performer']['score']:.2f})")

print("\n🎯 改进建议:")
avg_accuracy = df_raw['accuracy_rate'].mean()
avg_coverage = df_raw['feedback_coverage'].mean()

if avg_accuracy < 50:
    print(f"  • 准确率偏低 ({avg_accuracy:.1f}%)，建议优化反馈质量")
elif avg_accuracy > 80:
    print(f"  • 准确率较高 ({avg_accuracy:.1f}%)，表现良好")
else:
    print(f"  • 准确率中等 ({avg_accuracy:.1f}%)，有提升空间")

if avg_coverage < 60:
    print(f"  • 反馈覆盖率偏低 ({avg_coverage:.1f}%)，建议增加反馈收集")
elif avg_coverage > 85:
    print(f"  • 反馈覆盖率很高 ({avg_coverage:.1f}%)，覆盖充分")
else:
    print(f"  • 反馈覆盖率良好 ({avg_coverage:.1f}%)")

# 识别问题文档
problem_docs = df_raw[df_raw['accuracy_rate'] < 30]['document_name'].unique()
if len(problem_docs) > 0:
    print(f"  • 需要重点关注的低准确率文档 ({len(problem_docs)} 个):")
    for doc in problem_docs[:3]:  # 只显示前3个
        print(f"    - {doc[:50]}...")

print("=" * 80)
print(f"📅 报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("✅ 分析完成！")