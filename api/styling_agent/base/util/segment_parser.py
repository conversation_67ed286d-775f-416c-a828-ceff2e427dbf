from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple


@dataclass
class Segment:
    seg_id: str
    rev_id: Optional[str]
    kind: str           # "INS" | "BASE"
    start: int
    end: int
    # ↓ 仅当 include_table_info=True 才写入
    table: Optional[str] = None
    row: Optional[int] = None
    col: Optional[int] = None


def _iter_paragraphs(elements: List[Dict[str, Any]],
                     ctx: Tuple[Optional[str], Optional[int], Optional[int]]
                     = (None, None, None)):
    """深度优先遍历，返回 (paragraph_dict, table_id, row, col)"""
    tbl_id, row_idx, col_idx = ctx
    for el in elements:
        et = el.get("ElementType")
        if et == "Paragraph" or et == "List":
            yield el, tbl_id, row_idx, col_idx

        elif et == "Table":
            tid = el["ElementId"]
            for r, row in enumerate(el.get("Rows", [])):
                for c, cell in enumerate(row.get("Cells", [])):
                    yield from _iter_paragraphs(
                        cell.get("Elements", []), (tid, r, c)
                    )

        # ...

def _seg_text(seg: Dict[str, Any]) -> str:
    return seg.get("text") or seg.get("SegmentText", "")


def _seg_revision(seg: Dict[str, Any]) -> Tuple[Optional[str], str]:
    """返回 (rev_id, kind)；kind ∈ {INS, BASE}"""
    content_rev = seg.get("contentRevision") or {}
    if not content_rev and "ContentRevisionId" in seg:
        content_rev = {"revId": seg["ContentRevisionId"]}

    rev_id = content_rev.get("revId") or None
    if isinstance(rev_id, str) and not rev_id.strip():
        rev_id = None
    kind = "INS" if content_rev and content_rev.get("type") == "ins" else "BASE"
    return rev_id, kind


def build_segments(
    doc: Dict[str, Any],
    *,
    include_table_info: bool = True
) -> Dict[str, Dict[str, Any]]:
    """
    解析整份 JSON 文档，返回:
        {para_id: {"text": str, "segments": List[Segment]}}
    """
    result: Dict[str, Dict[str, Any]] = {}

    for para, tbl, row, col in _iter_paragraphs(doc.get("Elements", [])):
        pid = para.get("ParaId") or para.get("elementId") or para.get("ElementId")
        if not pid:
            continue

        seg_objs, parts, cursor = [], [], 0
        for s in para.get("Segments") or para.get("segments", []):
            txt = _seg_text(s)
            if not txt:
                continue

            rev_id, kind = _seg_revision(s)

            seg = Segment(
                seg_id=s.get("segId") or s.get("SegmentId"),
                rev_id=rev_id,
                kind=kind,
                start=cursor,
                end=cursor + len(txt),
            )

            if include_table_info:
                seg.table, seg.row, seg.col = tbl, row, col

            seg_objs.append(seg)
            parts.append(txt)
            cursor += len(txt)

        # merged: List[Segment] = list(seg_objs)
        # for sg in seg_objs:
        #     if (merged and merged[-1].rev_id is None and sg.rev_id is None
        #             and merged[-1].kind == sg.kind == "BASE"
        #             and merged[-1].end == sg.start):
        #         merged[-1].end = sg.end
        #     else:
        #         merged.append(sg)

        # result[pid] = {"text": "".join(parts), "segments": merged}
        result[pid] = {"text": "".join(parts), "segments": seg_objs}

    return result


@dataclass
class Segment2:
    seg_id: str
    start : int            # 段落内字符起
    end   : int            # 段落内字符终 (半开)


def build_para_map_lean(doc: dict) -> dict[str, dict]:
    full = build_segments(doc, include_table_info=False)

    for meta in full.values():
        meta["segments"] = [
            Segment2(s.seg_id, s.start, s.end)
            for s in meta["segments"]
        ]
    return full


if __name__ == "__main__":
    sample = {
        "Elements": [
            {"ElementType": "Paragraph", "ElementId": "P0",
             "Segments": [{"SegmentId": "P0_0", "SegmentText": "Hello"}]},
            {"ElementType": "Table", "ElementId": "T1",
             "Rows": [{"Cells": [
                 {"Elements": [{"ElementType": "Paragraph", "ElementId": "P1",
                                 "Segments": [{"SegmentId": "P1_0",
                                               "SegmentText": "C00"}]}]},
                 {"Elements": [{"ElementType": "Paragraph", "ElementId": "P2",
                                 "Segments": [{"SegmentId": "P2_0",
                                               "SegmentText": "C01",
                                               "ContentRevisionId": "rev"}]}]}
             ]}]}
        ]
    }

    mapping = build_segments(sample)
    assert mapping["P0"]["text"] == "Hello"
    assert mapping["P1"]["segments"][0].table == "T1"
    assert mapping["P1"]["segments"][0].row == 0
    assert mapping["P2"]["segments"][0].rev_id == "rev"
    print("Self-test passed.")
