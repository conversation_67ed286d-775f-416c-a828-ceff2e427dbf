from typing import Any, Literal, overload

from loguru import logger

from base.model import ParaR<PERSON>ult, ParaState


context: "ContextManager" = None  # Global context variable


def get_context() -> "ContextManager":
    """
    Get the current ContextManager instance.

    Returns:
        The current ContextManager instance.

    Raises:
        RuntimeError: If no ContextManager has been set for the current context.
    """
    global context
    if context is None:
        # Auto-initialize with empty context if none exists
        context = ContextManager()
        logger.debug("Auto-initialized new ContextManager instance")
    return context


KEYS = Literal[
    "state",
    "s3_file_path",
    "xml_content",
    "revision_paragraphs_xml",
    "paragraphs_order",
    "fulltext",
    "para_state",
]


class ContextManager:
    def __init__(self):
        self.state = {}
        self._s3_file_path = ""
        self._xml_content: dict = {}
        self._revision_paragraphs_xml: list[str] = []
        self._paragraphs_order: list[str] = []
        self._fulltext: str = ""
        self._para_context: dict[str, ParaState] = {}
        self._para_result: list[ParaResult] = []
        self._patch_result: dict = {}

    @property
    def s3_file_path(self) -> str:
        return self._s3_file_path

    @property
    def xml_content(self) -> dict:
        return self._xml_content

    @property
    def revision_paragraphs_xml(self) -> list[str]:
        return self._revision_paragraphs_xml

    @property
    def paragraphs_order(self) -> list[str]:
        return self._paragraphs_order

    @property
    def fulltext(self) -> str:
        return self._fulltext

    @property
    def para_results(self) -> list[ParaResult]:
        return self._para_result

    @property
    def patch_result(self) -> dict:
        return self._patch_result

    @overload
    def set(self, key: Literal["xml_content"], value: dict) -> None: ...

    @overload
    def set(self, key: Literal["fulltext", "s3_file_path"], value: str) -> None: ...

    @overload
    def set(self, key: Literal["para_state"], value: ParaState) -> None: ...

    @overload
    def set(
        self,
        key: Literal["revision_paragraphs_xml", "paragraphs_order"],
        value: list[str],
    ) -> None: ...

    def set(
        self,
        key: KEYS,
        value: Any,
    ) -> None:
        """
        Set a value in the context data.

        Args:
            key: The key to set in the context data
            value: The value to set for the given key
        """
        match key:
            case "xml_content":
                if not isinstance(value, dict):
                    raise TypeError("xml_content must be a dictionary")
                self.set_xml_content(value)
            case "revision_paragraphs_xml":
                if not isinstance(value, list):
                    raise TypeError("revision_paragraphs_xml must be a list")
                self._revision_paragraphs_xml = value
            case "paragraphs_order":
                if not isinstance(value, list):
                    raise TypeError("paragraphs_order must be a list")
                self._paragraphs_order = value
            case "fulltext":
                if not isinstance(value, str):
                    raise TypeError("fulltext must be a string")
                self._fulltext = value
            case "para_state":
                if not isinstance(value, ParaState):
                    raise TypeError("para_state must be a dictionary")
                self.set_para_state(para_id=value.para_id, state=value)
            case "s3_file_path":
                if not isinstance(value, str):
                    raise TypeError("s3_file_path must be a string")
                self._s3_file_path = value
            case _:
                raise KeyError(f"Invalid key: {key}")

    def set_xml_content(self, xml_content: dict) -> None:
        """
        Set the XML content in the context data.

        Args:
            xml_content: Dictionary containing XML content
        """
        self._xml_content = xml_content
        for element in xml_content.get("Elements", []):
            if "ElementId" not in element:
                continue
            para_id = element["ElementId"]
            para_content = element.get("PlainText", "")
            para_type = element.get("ElementType", "paragraph").lower()
            self._para_context[para_id] = ParaState(para_id=para_id, para_content=para_content, para_type=para_type)

    def get_xml_content(self) -> dict:
        """
        Get the XML content from the context data.

        Returns:
            The XML content as a dictionary
        """
        return self._xml_content

    def get_para_states(self) -> list[ParaState]:
        return list(self._para_context.values())

    def get_para_state(self, para_id: str) -> ParaState | None:
        """
        Get the state of a paragraph by its ID.

        Args:
            para_id: The ID of the paragraph

        Returns:
            The state of the paragraph as a ParaState dictionary, or None if not found
        """
        return self._para_context.get(para_id)

    def set_para_state(self, para_id: str, state: ParaState) -> None:
        """
        Set the state of a paragraph by its ID.

        Args:
            para_id: The ID of the paragraph
            state: The state to set for the paragraph
        """
        if not isinstance(state, ParaState):
            raise TypeError("state must be an instance of ParaState")
        self._para_context[para_id] = state

    def set_patch_result(self, patch_result: dict) -> None:
        self._patch_result = patch_result

    def dict(self) -> dict:
        """
        Convert the context data to a dictionary.

        Returns:
            A dictionary representation of the context data
        """
        return {
            "s3_file_path": self._s3_file_path,
            "xml_content": self._xml_content,
            "revision_paragraphs_xml": self._revision_paragraphs_xml,
            "paragraphs_order": self._paragraphs_order,
            "fulltext": self._fulltext,
            "para_context": {k: v.model_dump() for k, v in self._para_context.items()},
            "para_results": [p.model_dump() for p in self._para_result],
            "patch_result": self._patch_result,
        }
