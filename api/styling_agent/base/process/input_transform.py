from typing import Any, Dict, List

from loguru import logger
from lxml import etree

from base.util.link import extract_links


def extract_paragraphs_xml(data: Dict, revision_toggle: bool = False) -> tuple[list[str], list[str]]:
    """Extract paragraph and list fragments from the serialized XML-like JSON document,
    convert them to XML strings in batches, and return both the batched XML chunks
    and their original order IDs.

    Args:
        data (Dict): Raw JSON obtained from XML serialization, containing:
            - ``Elements``: List of top-level document elements (paragraphs, lists, tables, etc.).
            - ``Annotations``: Dict mapping element IDs to their annotation details.
            - ``NativeRevisions``: Dict of revision metadata for native writers.
        revision_toggle (bool, optional): Whether to include revision information when
            building paragraph content. Defaults to False.

    Returns:
        tuple[list[str], list[str]]:
            - first item: List of XML strings, each containing **up to 50** paragraphs;
            - second item: Ordered list of each paragraph's ``ParaId``, preserving the
              sequence in which they appeared in the source document.

    Note:
        Tables are currently **skipped** but can be enabled by uncommenting the inline
        processing code if cell-level paragraphs are needed in the future.
    """
    data_elements = data.get("Elements", [])
    data_annotations = data.get("Annotations", {})
    native_revision = data.get("NativeRevisions", {})
    paragraphs = []
    for element in data_elements:
        element_type = element.get("ElementType")
        if element_type == "Paragraph" or element_type == "List":
            para = paragraph_helper(element, data_annotations, native_revision, revision_toggle)
            if para:
                paragraphs.append(para)
            continue
        if element_type == "Table":
            continue
            # rows = element.get("Rows", [])
            # table_id = element.get("ElementId", "")
            # for row in rows:
            #     cells = row.get("Cells", [])
            #     row_id = row.get("RowId", "")
            #     for cell in cells:
            #         cell_id = cell.get("CellId", "")
            #         # Process paragraph here
            #         for cpara in cell.get("Elements", []):
            #             cell_para = paragraph_helper(
            #                 cpara,
            #                 data_annotations,
            #                 native_revision,
            #                 revision_toggle,
            #             )
            #             if cell_para:
            #                 # cell_para["ParaId"] = f"{table_id}_{row_id}_{cell_id}"
            #                 paragraphs.append(cell_para)

    # break paragraphs into list up to 50 elemnet each
    split_xmls = []
    for i in range(0, len(paragraphs), 50):
        split_paras = paragraphs[i : i + 50]
        split_xmls.append(convert_json_to_xml(split_paras))

    paragraphs_order = [para["ParaId"] for para in paragraphs]
    return split_xmls, paragraphs_order


def extract_full_text(data: Dict) -> str:
    """
    Extracts the full text content from the provided data dictionary.

    Args:
        data (Dict): The data dictionary containing elements and annotations.

    Returns:
        str: The full text content extracted from the data.
    """
    full_text = []
    for element in data.get("Elements", []):
        if element.get("ElementType") == "Paragraph":
            text = element.get("PlainText", "")
            if text:
                full_text.append(text)
    return "\n".join(full_text)


def convert_json_to_xml(data_list: List[dict], pretty_print: bool = False) -> str:
    outer = etree.Element("Xml")

    for data in data_list:
        paragraph = etree.SubElement(outer, "Paragraph", paraId=data["ParaId"])
        paragraph.text = data["Text"]

        p_style = data.get("Style", {})
        if p_style:
            if p_style.get("Style") != "Normal" and p_style.get("Style") != "":
                paragraph.set("Style", p_style.get("Style"))

        links = data.get("Links", [])
        if links:
            links_elem = etree.SubElement(paragraph, "Links")
            for link in links:
                link_elem = etree.SubElement(links_elem, "Link")
                link_elem.set("LinkId", link["Id"])
                link_elem.set("Uri", link["Uri"])

        runs = data.get("Runs", [])
        if runs:
            run_elems = etree.SubElement(paragraph, "Runs")
            for run in runs:
                style = run.get("style", {})
                if run["Text"] == "" or (not style.get("Bold") and not style.get("Italic")):
                    continue

                run_elem = etree.SubElement(run_elems, "Run", Bold="false", Italic="false", Underline="false")
                run_elem.text = run["Text"]
                if style.get("Bold"):
                    run_elem.set("Bold", "true")
                if style.get("Italic"):
                    run_elem.set("Italic", "true")
                if style.get("Underline"):
                    run_elem.set("Underline", "true")

    return etree.tostring(outer, pretty_print=pretty_print, encoding="unicode")


def paragraph_helper(element, data_annotations, native_revision, revision_toggle):
    text = element.get("PlainText", "")
    skip_flag = skip_paragraphs(text)
    if skip_flag:
        return
    para = {
        "ParaId": element["ElementId"],
        "Text": text,
        "Style": element.get("Properties", {}),
    }
    hyperlinks = set()
    comments = set()
    merged_runs = []

    for run in element.get("Segments", []):
        # skip if is delete run
        if run.get("ContentRevisionId") and native_revision.get(run["ContentRevisionId"])["RevType"] == "DeletedRun":
            continue
        # skip if revision toggle is on and run does not have ContentRevisionId
        if revision_toggle and not run.get("ContentRevisionId"):
            continue

        if run.get("HyperlinkId") != "":
            hyperlinks.add(run.get("HyperlinkId"))

        if run.get("CommentIds"):
            for comment_id in run.get("CommentIds", []):
                comments.add(comment_id)

        run_style = run.get("Properties", {})
        current_bold = True if run_style.get("Bold") == "true" else False
        current_italic = True if run_style.get("Italic") == "true" else False
        current_underline = True if run_style.get("Underline") == "true" else False

        # Check if we can merge with the previous run
        can_merge = False
        if merged_runs:
            last_style = merged_runs[-1].get("style", {})
            last_bold = last_style.get("Bold", False)
            last_italic = last_style.get("Italic", False)
            last_underline = last_style.get("Underline", False)

            # Compare styles - both must match exactly
            if last_bold == current_bold and last_italic == current_italic and last_underline == current_underline:
                can_merge = True

        if can_merge:
            # Merge with previous run
            merged_runs[-1]["Text"] += run.get("SegmentText", "")
        else:
            # Create new run
            merged_runs.append(
                {
                    "Text": run.get("SegmentText", ""),
                    "style": {
                        "Bold": current_bold,
                        "Italic": current_italic,
                        "Underline": current_underline,
                    },
                }
            )
    if not merged_runs:
        return  # skip if no runs
    para["Runs"] = merged_runs
    links = []
    if hyperlinks:
        links.extend(
            [
                {
                    "Id": hyperlink_id,
                    "Uri": data_annotations["Hyperlinks"].get(hyperlink_id, {}).get("Uri", ""),
                    "Targets": data_annotations["Hyperlinks"].get(hyperlink_id, {}).get("Targets", []),
                }
                for hyperlink_id in hyperlinks
            ]
        )
    if comments:
        for comment_id in comments:
            text = data_annotations["Comments"].get(comment_id, {}).get("Text", "")
            # Extract all links from the comment text
            comment_links = extract_links(text)
            for link in comment_links:
                links.append(
                    {
                        "Id": comment_id,
                        "Uri": link,
                    }
                )
    para["Links"] = links
    return para


def skip_paragraphs(text: str) -> bool:
    """Check if the paragraph text should be skipped based on certain conditions."""
    if not text:
        return True
    if text.endswith("End of Document"):
        return True
    # Add more conditions as needed
    return False


def extract_paragraph_data(p_id: str, xml: str, xml_content: Dict[str, Any]) -> Dict[str, Any]:
    """
    Extract all paragraph data (content, links, styles) in a single operation.
    This eliminates redundant XML parsing and improves performance.

    Returns:
        Dict with keys: content, links, par_style_text, run_style_text
    """
    result = {"content": "", "links": [], "par_style_text": "", "run_style_text": ""}

    if not p_id:
        return result

    # Extract content and styles from XML string
    if xml:
        try:
            root = etree.fromstring(xml)
            paragraph = root.find(f".//Paragraph[@paraId='{p_id}']")

            if paragraph is not None:
                # Extract content
                result["content"] = paragraph.text or ""

                # Extract paragraph style
                par_style = paragraph.get("Style")
                result["par_style_text"] = (
                    f"<pStyle>{par_style}</pStyle>" if par_style and not par_style.startswith("Normal") else ""
                )

                # Extract run styles full
                for element in xml_content.get("Elements", []):
                    if element.get("ElementId") == p_id:
                        run_texts = []
                        for segment in element.get("Segments", []):
                            hyperlink_flag = True if segment.get("HyperlinkId") else False
                            text = segment.get("SegmentText", "")
                            style = segment.get("Properties", {})
                            if hyperlink_flag:
                                run_texts.append(text)
                                continue
                            if style.get("Bold") == "true":
                                text = f"<b>{text}</b>"
                            if style.get("Italic") == "true":
                                text = f"<i>{text}</i>"
                            if style.get("Underline") == "true":
                                text = f"<u>{text}</u>"
                            run_texts.append(text)
                        result["run_style_text"] = "".join(run_texts)
                        break

        except etree.XMLSyntaxError:
            logger.error(f"Invalid XML format for paragraph ID {p_id}. Unable to parse.")

    # Extract links from xml_content dict
    if xml_content:
        result["links"] = _extract_links_from_content(p_id, xml_content)

    return result


def _extract_links_from_content(p_id: str, xml_content: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Helper function to extract links from xml_content dictionary."""
    elements = xml_content.get("Elements", [])
    annotations = xml_content.get("Annotations", {})

    # Find the target paragraph
    target_element = None
    for element in elements:
        if element.get("ElementId") == p_id:
            target_element = element
            break

    if not target_element:
        return []

    result = []
    seen_hyperlinks = set()
    seen_comments = set()

    hyperlinks_data = annotations.get("Hyperlinks", {})
    comments_data = annotations.get("Comments", {})

    for run in target_element.get("Segments", []):
        # Process hyperlinks
        hyperlink_id = run.get("HyperlinkId")
        if hyperlink_id and hyperlink_id not in seen_hyperlinks:
            seen_hyperlinks.add(hyperlink_id)
            link_data = hyperlinks_data.get(hyperlink_id, {})
            if link_data:
                result.append(
                    {
                        "Type": "Hyperlink",
                        "Uri": link_data.get("Uri", ""),
                        "Targets": link_data.get("Targets", []),
                    }
                )

        # Process comments
        comment_ids = run.get("CommentIds", [])
        for comment_id in comment_ids:
            if comment_id not in seen_comments:
                seen_comments.add(comment_id)
                comment_data = comments_data.get(comment_id, {})
                if comment_data:
                    text = comment_data.get("Text", "")
                    # Extract all links from the comment text
                    links = extract_links(text)
                    for link in links:
                        result.append(
                            {
                                "Type": "Comment",
                                "Uri": link,
                                "Targets": [comment_id],
                            }
                        )

    return result
