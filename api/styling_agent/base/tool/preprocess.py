from base.process.input_transform import extract_full_text, extract_paragraphs_xml

from .base import Tool


class PreProcessTool(Tool):
    """Context is: # Integrate - base.tool\n
    PreProcessTool is integrated into base.tool for extracting and processing revision paragraphs from XML documents:\n
    - Use PreProcessTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - xml_content (str): The XML content of the document, retrieved from `self.context_manager`.

    Provides(context):
        - revision_paragraphs_xml (list[str]): List of the extracted revision paragraphs XML content.
        - paragraphs_order (list[str]): List of the extracted paragraph order.
        - fulltext (str): The full text content of the document.

    Args:
        - revision_toggle (bool): A flag to indicate whether to only extract revision paragraphs, retrieved from `self.parameters`. Defaults to False.
    """

    example = """```python\n
    # Creating a text data\n
    from base.tool import PreProcessTool
    # prepare your data in dictionary format as we previously mentioned
    rrt = PreProcessTool(**data)
    rrt.run()\n
    ```"""

    def run(self):
        try:
            xml_content = self.context_manager.get_xml_content()
            if not xml_content:
                self.logger.error("No XML content provided.")
                return {}
            revision_toggle = self.parameters.get("revision_toggle", False)
            revision_paragraphs_xml, paragraphs_order = extract_paragraphs_xml(xml_content, revision_toggle)
            fulltext = extract_full_text(xml_content)
            self.context_manager.set("revision_paragraphs_xml", revision_paragraphs_xml)
            self.context_manager.set("paragraphs_order", paragraphs_order)
            self.context_manager.set("fulltext", fulltext)
        except Exception as e:
            self.logger.error(f"{self.name}: Error extracting revision paragraphs: {e}.")
            raise e
