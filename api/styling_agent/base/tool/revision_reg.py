import json
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List, Optional, TypedDict

import boto3

from base.example.revision_reg import REVISION_REG_EXAMPLE
from base.process.input_transform import extract_paragraph_data
from base.prompt.tooling_prompt import prompt_revision_reg_check
from base.util.author_check import author_helper
from base.util.convert import convert_yaml_to_json_if_possible

from .base import LLMTool

s3 = boto3.client("s3")

# Constants for style text
HEADING1_STYLE = "<pStyle>Heading1</pStyle>"
TITLE_FONT_SIZE = 14


class RevisionRegToolAnswerData(TypedDict):
    para_id: str
    para_content: str
    par_style_text: str
    run_style_text: str
    links: list[dict]


class RevisionRegToolAnswerFlags(TypedDict):
    quotation: bool
    link: bool
    abbr: bool
    capital: bool
    leg_ac: bool
    heading: bool
    emphasis: bool
    grammar: bool
    spelling: bool
    num: bool


class RevisionRegToolAnswer(RevisionRegToolAnswerData, RevisionRegToolAnswerFlags):
    pass


RevisionRegToolReturn = list[RevisionRegToolAnswer]


class RevisionRegTool(LLMTool):
    """
    Context: # Integrate - base.tool

    You should use this tool to identify which task to run.
    RevisionRegTool has integrated base.tool for identification.
    There is one aspect of RevisionRegTool: understanding XML with the user's document:
    - Use RevisionRegTool.run to execute.

    Requires (context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - xml_content (str): The XML content of the document, retrieved from `self.context_manager`.
        - revision_paragraphs_xml (list[str]): The list of revision paragraphs, retrieved from `self.context_manager`.

    Provides (context):
        For each paragraph in the xml_content:
        - para_state (ParaState): The updated paragraph state after running the styling tool.
          - para_content (str): The content of the paragraph.
          - modified_para_content (str): The modified content of the paragraph.
          - commented_para_content (str): The content with comments added, if applicable.
          - run_style_text (str): The run style text of the paragraph.
          - par_style_text (str): The paragraph style text.
          - patches (list): The patch operations to be applied to the Word document.

    """

    example = REVISION_REG_EXAMPLE

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化各种标志处理器
        self.navigation_section_processor = NavigationSectionFlagProcessor(logger=self.logger)
        self.reference_processor = ReferenceFlagProcessor(logger=self.logger)
        self.other_resources_processor = OtherResourcesFlagProcessor(logger=self.logger)
        self.title_processor = TitleFlagProcessor(logger=self.logger)
        self.list_processor = ListParagraphFlagProcessor(logger=self.logger)
        self.author_paragraph_processor = AuthorParagraphFlagProcessor(logger=self.logger)
        self.cover_page_processor = CoverPageProcessor(logger=self.logger)

    def run(self) -> RevisionRegToolReturn:
        xml_content = self.context_manager.xml_content
        revision_paragraphs = self.context_manager.revision_paragraphs_xml

        if not revision_paragraphs:
            return []

        # Use concurrent execution for LLM predictions
        llm_answers = self._execute_llm_predictions(revision_paragraphs)

        # Process results efficiently
        results = self._process_llm_results(llm_answers, revision_paragraphs, xml_content)
        results_map: dict[str, RevisionRegToolAnswer] = {}

        # Grammar check and spelling check - by default, all paragraphs are checked
        for answer in results:
            results_map[answer["para_id"]] = answer
            answer["grammar"] = True
            answer["spelling"] = True

        # Apply post-processing using flag processors
        reference_flags = self.reference_processor.apply_flags(xml_content, results_map)
        other_resources_flags = self.other_resources_processor.apply_flags(xml_content, results_map)
        title_flags = self.title_processor.apply_flags(xml_content, results_map)
        heading_flags = self.navigation_section_processor.apply_flags(xml_content, results_map)
        list_flags = self.list_processor.apply_flags(xml_content, results_map)
        author_flags = self.author_paragraph_processor.apply_flags(xml_content, results_map)
        cover_page_flags = self.cover_page_processor.apply_flags(xml_content, results_map)

        # Merge all flags back into results
        final_results = self._merge_override_results(
            results,
            reference_flags
            | other_resources_flags
            | heading_flags
            | title_flags
            | list_flags
            | author_flags
            | cover_page_flags,
        )

        for result in final_results:
            para_state = self.context_manager.get_para_state(result["para_id"])
            if not para_state:
                self.logger.error(f"{self.name}: No paragraph found with ID {result['para_id']}.")
                continue
            para_state.modified_para_content = result.get("modified_para_content", para_state.para_content)
            para_state.commented_para_content = result.get("commented_para_content", para_state.para_content)
            para_state.run_style_text = result.get("run_style_text", "")
            para_state.par_style_text = result.get("par_style_text", "")
            para_state.links = result.get("links", [])

            for flag_name in RevisionRegToolAnswerFlags.__annotations__:
                if result.get(flag_name, False):
                    para_state.enabled_tools.append(flag_name)

        return final_results

    def _merge_override_results(
        self,
        original_results: RevisionRegToolReturn,
        overrides: dict[str, RevisionRegToolAnswer],
    ) -> RevisionRegToolReturn:
        """Merge overrides back into the original results."""
        final_results = []

        for result in original_results:
            para_id = result["para_id"]
            if para_id in overrides:
                # Use the overridden version for reference paragraphs
                final_results.append(overrides[para_id])
            else:
                # Use the original result for non-reference paragraphs
                final_results.append(result)

        return final_results

    def _execute_llm_predictions(self, revision_paragraphs: List[str]) -> List[str]:
        """Execute LLM predictions concurrently and return ordered results."""
        llm_answers = [""] * len(revision_paragraphs)

        with ThreadPoolExecutor(max_workers=min(5, len(revision_paragraphs))) as executor:
            try:
                future_to_index = {
                    executor.submit(self.llm_predict, revision_paragraph): idx
                    for idx, revision_paragraph in enumerate(revision_paragraphs)
                }

                for future in as_completed(future_to_index):
                    idx = future_to_index[future]
                    try:
                        llm_answers[idx] = future.result()
                    except Exception as e:
                        self.logger.error(f"{self.name}: Error in LLM prediction for paragraph {idx}: {e}")

            except Exception as e:
                self.logger.error(f"{self.name}: Error in concurrent LLM execution: {e}")

        return llm_answers

    def _process_llm_results(
        self,
        llm_answers: List[str],
        revision_paragraphs: List[str],
        xml_content: Dict[str, Any],
    ) -> RevisionRegToolReturn:
        """Process LLM results and extract paragraph data efficiently."""
        revision_reg_map = []

        for idx, llm_answer in enumerate(llm_answers):
            if not llm_answer:
                continue

            processed_answers = self.post_process(llm_answer)
            if not processed_answers:
                continue

            current_paragraph_xml = revision_paragraphs[idx]

            for answer in processed_answers:
                processed_answer = self._process_single_answer(answer, current_paragraph_xml, xml_content)
                if processed_answer:
                    revision_reg_map.append(processed_answer)

        return revision_reg_map

    def _process_single_answer(
        self,
        answer: RevisionRegToolAnswer,
        paragraph_xml: str,
        xml_content: Dict[str, Any],
    ) -> Optional[RevisionRegToolAnswer]:
        """Process a single answer and extract all required data."""
        para_id = answer.get("para_id")
        if not para_id:
            return None

        # Ensure para_id is string
        para_id_str = str(para_id)
        answer["para_id"] = para_id_str

        try:
            # Extract all paragraph data in one go - eliminates redundant XML parsing
            para_data = extract_paragraph_data(para_id_str, paragraph_xml, xml_content)

            answer.update(
                {
                    "para_content": para_data["content"],
                    "links": para_data["links"],
                    "par_style_text": para_data["par_style_text"],
                    "run_style_text": para_data["run_style_text"],
                }
            )

            return answer

        except Exception as e:
            self.logger.error(f"{self.name}: Error processing answer for para_id {para_id_str}: {e}")
            return None

    def prompt_template(self, text: str):
        return prompt_revision_reg_check.format(text)

    def post_process(self, input_str: str) -> RevisionRegToolReturn:
        """Convert YAML/JSON string to structured data with error handling."""
        try:
            json_str = convert_yaml_to_json_if_possible(input_str)
            data = json.loads(json_str)
            return data
        except Exception as e:
            self.logger.error(f"Error in post_process: {e}")
        return []


class BaseFlagProcessor(ABC):
    """抽象基类，用于处理标志设置的通用逻辑"""

    def __init__(self, logger=None):
        self.logger = logger

    def reset_all_flags(self, result: RevisionRegToolAnswer, default: bool = False) -> RevisionRegToolAnswer:
        """Reset all flags in the result to default values.

        This method is used to ensure that all flags are set to a known state before applying specific logic.

        Args:
            result (RevisionRegToolAnswer): 原始结果
            default (bool): 是否将所有标志设置为默认值，默认为False
        Returns:
            RevisionRegToolAnswer: 重置后的结果"""
        result_copy = result.copy()
        for flag_name in RevisionRegToolAnswerFlags.__annotations__:
            result_copy[flag_name] = default
        return result_copy

    @abstractmethod
    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        """应用特定的标志设置逻辑"""
        pass


class NavigationSectionFlagProcessor(BaseFlagProcessor):
    """处理导航部分标志的具体实现"""

    def _find_jump_section_index(self, elements: list) -> Optional[int]:
        """查找jump to section元素的索引"""
        for i, element in enumerate(elements):
            plain_text = element.get("PlainText", "")
            if plain_text.lower() == "jump to section":
                return i
        return None

    def _parse_heading_names(self, section_text: str) -> list[str]:
        """解析标题名称集合"""
        heading_items = section_text.split("|")
        return [item.strip() for item in heading_items if item.strip()]

    def _find_heading_elements(
        self,
        elements: list,
        jump_idx: int,
        heading_names: list[str],
        result_map: dict[str, RevisionRegToolAnswer],
    ) -> list[RevisionRegToolAnswer]:
        """查找匹配的标题元素"""
        heading_elements_result = []
        remaining_names = heading_names.copy()

        for element in elements[jump_idx + 1 :]:
            plain_text: str = element.get("PlainText", "")
            plain_text = plain_text.strip()
            if not plain_text:
                continue

            if plain_text not in remaining_names:
                continue

            remaining_names.remove(plain_text)
            element_id = element.get("ElementId")
            heading_element_result = result_map.get(element_id)

            if not heading_element_result:
                if self.logger:
                    self.logger.warning(f"Element ID {element_id} not found in result map")
                continue

            heading_elements_result.append(heading_element_result)

            if len(remaining_names) == 0:
                break

        return heading_elements_result

    def _apply_heading_flags_to_elements(
        self, heading_elements: list[RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        """为标题元素应用标志设置"""
        overrides = {}
        for result in heading_elements:
            result_copy = self.reset_all_flags(result)
            result_copy["heading"] = True
            result_copy["par_style_text"] = HEADING1_STYLE
            para_id = result["para_id"]
            overrides[para_id] = result_copy
        return overrides

    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        """应用标题标志设置的主要逻辑"""
        elements = xml_content.get("Elements", [])
        overrides = {}

        # 查找jump section索引
        jump_idx = self._find_jump_section_index(elements)
        if jump_idx is None:
            return overrides

        # 处理标题元素 - 添加边界检查
        section_element_idx = jump_idx + 1
        if section_element_idx >= len(elements):
            # 如果jump section是最后一个元素，没有后续元素需要处理
            return overrides

        # 单独处理jump section元素
        jump_section_element = elements[jump_idx]
        jump_element_id = jump_section_element.get("ElementId")
        if jump_element_id in result_map:
            jump_section_result = self.reset_all_flags(result_map[jump_element_id])
            overrides[jump_element_id] = jump_section_result

        section_element = elements[section_element_idx]
        section_text = section_element.get("PlainText", "")
        section_element_id = section_element.get("ElementId")
        if section_element_id in result_map:
            section_result = self.reset_all_flags(result_map[section_element_id])
            overrides[section_element_id] = section_result

        heading_names = self._parse_heading_names(section_text)
        heading_elements = self._find_heading_elements(elements, jump_idx, heading_names, result_map)
        heading_overrides = self._apply_heading_flags_to_elements(heading_elements)

        overrides.update(heading_overrides)
        return overrides


class ReferenceFlagProcessor(BaseFlagProcessor):
    """处理引用标志的具体实现"""

    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        """应用引用标志设置"""
        elements = xml_content.get("Elements", [])
        reference_overrides = {}

        for element in elements:
            plain_text = element.get("PlainText", "")
            if not plain_text.startswith("References:"):
                continue

            element_id = element.get("ElementId")
            if element_id not in result_map:
                if self.logger:
                    self.logger.warning(f"Element ID {element_id} not found in result map")
                continue

            reference_result = self.reset_all_flags(result_map[element_id])
            reference_result["link"] = True
            reference_overrides[element_id] = reference_result

        return reference_overrides


class OtherResourcesFlagProcessor(BaseFlagProcessor):
    """处理其他资源标志的具体实现"""

    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        """应用其他资源标志设置"""
        elements = xml_content.get("Elements", [])
        overrides = {}
        other_resources_idx = None

        for i, element in enumerate(elements):
            plain_text = element.get("PlainText", "")
            if plain_text == "Other Resources":
                other_resources_idx = i
                break

        if other_resources_idx is None:
            return overrides

        # 设置Other Resources标题元素的标志
        other_resources_element_id = elements[other_resources_idx].get("ElementId")
        if other_resources_element_id in result_map:
            other_resources_element_result = self.reset_all_flags(result_map[other_resources_element_id])
            overrides[other_resources_element_id] = other_resources_element_result

        # 设置后续元素的标志
        for j in range(other_resources_idx + 1, len(elements)):
            element = elements[j]
            element_id = element.get("ElementId")
            if element_id not in result_map:
                if self.logger:
                    self.logger.warning(f"Element ID {element_id} not found in result map")
                continue

            reference_result = self.reset_all_flags(result_map[element_id])
            reference_result["link"] = True
            overrides[element_id] = reference_result

        return overrides


class TitleFlagProcessor(BaseFlagProcessor):
    """处理文章标题的具体实现"""

    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        """应用标题样式标志设置"""
        elements = xml_content.get("Elements", [])
        overrides = {}

        for element in elements:
            segments = element.get("Segments", [])
            style = element.get("Properties", {}).get("Style")
            element_id = element.get("ElementId")
            result = result_map.get(element_id)
            if not result:
                if self.logger:
                    self.logger.warning(f"Element ID {element_id} not found in result map")
                continue
            if style == "Heading1":
                result_copy = self.reset_all_flags(result)
                result_copy["heading"] = True
                result_copy["par_style_text"] = HEADING1_STYLE
                overrides[element_id] = result_copy
                continue

            if not segments:
                if self.logger:
                    self.logger.warning(f"Element ID {element_id} has no segments")
                continue

            for segment in segments:
                segment_properties = segment.get("Properties", {})
                font_size = segment_properties.get("FontSize")
                if font_size and int(font_size) / 2 == TITLE_FONT_SIZE:  # xml font size is in half-points
                    result_copy = self.reset_all_flags(result)
                    overrides[element_id] = result_copy
                    break

        return overrides


class CoverPageProcessor(BaseFlagProcessor):
    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        elements = xml_content.get("Elements", [])
        overrides = {}

        for element in elements:
            style = element.get("Properties", {}).get("Style")
            if style == "Heading1":
                break

            element_id = element.get("ElementId")
            result = result_map.get(element_id)
            if not result:
                if self.logger:
                    self.logger.warning(f"Element ID {element_id} not found in result map")
                continue

            result_copy = self.reset_all_flags(result)
            overrides[element_id] = result_copy

        return overrides


class ListParagraphFlagProcessor(BaseFlagProcessor):
    """Concrete implementation for handling list paragraph flags."""

    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        """Apply list paragraph flag settings."""
        elements = xml_content.get("Elements", [])
        overrides = {}

        for element in elements:
            element_id = element.get("ElementId")
            if element_id not in result_map:
                continue

            # Check if this is a list paragraph
            element_type = element.get("ElementType")
            properties = element.get("Properties", {})
            has_list_info = "ListInfo" in properties

            # If ElementType is List or Properties contains ListInfo, treat as list paragraph
            is_list_paragraph = element_type == "List" or has_list_info

            if is_list_paragraph:
                # Get the original result and reset all flags
                result = result_map[element_id]
                result_copy = self.reset_all_flags(result, default=False)

                result_copy["spelling"] = result["spelling"]
                result_copy["abbr"] = result["abbr"]
                result_copy["link"] = result["link"]

                overrides[element_id] = result_copy

        return overrides


class AuthorParagraphFlagProcessor(BaseFlagProcessor):
    def apply_flags(
        self, xml_content: dict, result_map: dict[str, RevisionRegToolAnswer]
    ) -> dict[str, RevisionRegToolAnswer]:
        elements = xml_content.get("Elements", [])
        overrides = {}

        for element in elements:
            plain_text = element.get("PlainText", "")
            if not author_helper.is_author_paragraph(plain_text):
                continue

            element_id = element.get("ElementId")
            if element_id not in result_map:
                if self.logger:
                    self.logger.warning(f"Element ID {element_id} not found in result map")
                continue

            author_result = self.reset_all_flags(result_map[element_id])
            overrides[element_id] = author_result
            break

        return overrides
