from datetime import datetime, timezone

from base.util.comment_ops_builder2 import build_comment_ops
from base.util.edit_ops_builder import build_edit_ops
from base.util.segment_parser import build_para_map_lean

from .base import Tool


class PatchTool(Tool):
    """Context is: # Integrate - base.tool\n
    PatchTool is integrated into base.tool for generating word update patch operations for word documents from file path:\n
    - Use PatchTool run for execute.\n

    Requires(context):
        These parameters are obtained from `self.context_manager` and do not need to be passed explicitly:
        - xml_content (str): The XML content of the document, retrieved from `self.context_manager`.
        - para_results (List[ParaResult]): The paragraph results of the document, retrieved from `self.context_manager`.

    Provides(context):
        - patch_result (dict): A dictionary containing the patch operations to be applied to the Word document.

    Returns:
        - patch_json (dict): A dictionary containing the patch operations to be applied to the Word document.
    """

    example = """```python\n
    from base.tool import PatchTool\n
    # prepare your data in dictionary format as we previously mentioned
    wlt = PatchTool()
    patch_json = wlt.run()\n
    ```"""

    def run(self):
        xml_content = self.context_manager.xml_content
        para_states = self.context_manager.get_para_states()
        now_utc = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%SZ")
        default_rev = {"author": "Styling Agent", "date": now_utc}

        para_results_map = {r.para_id: r for r in para_states}

        all_ops = []
        para_map = build_para_map_lean(xml_content)
        for para_id, meta in para_map.items():
            old_text = meta.get("text", "")
            if not old_text:
                continue

            segs = meta.get("segments", [])
            para_state = para_results_map.get(para_id, None)
            if para_state is None:
                continue

            new_text = para_state.modified_para_content
            commented = para_state.commented_para_content
            run_style_text = para_state.run_style_text
            patches = para_state.patches
            all_ops.extend(build_edit_ops(old_text, new_text, segs, default_rev))
            if patches:
                all_ops.extend(patches)

            if commented:
                ops = build_comment_ops(
                    old_text,
                    commented,
                    run_style_text,
                    para_map[para_id]["segments"],
                )
                if len(ops) == 0:
                    self.logger.warning(f"No comment ops generated for para_id {para_id}.")
                else:
                    all_ops.extend(ops)

        result = {
            "schema": "word-text-patch",
            "version": "1.0",
            "documentId": "doc-001",  # This can be replaced with a dynamic value if needed
            # "documentId": structure.get("documentId", "doc-001"),
            "defaultRevision": default_rev,
            "operations": all_ops,
        }
        self.context_manager.set_patch_result(result)
        return result
