import unittest
from lxml import etree

# Import modules to test
from base.process.input_transform import (
    extract_paragraphs_xml,
    extract_paragraph_data,
    convert_json_to_xml,
)


class TestInputTransform(unittest.TestCase):
    """Test cases for input_transform module methods"""

    def setUp(self):
        """Set up test data"""
        self.maxDiff = None

        # Mock input data structure
        self.sample_data = {
            "Elements": [
                {
                    "ElementType": "Paragraph",
                    "ElementId": "TEST001",
                    "PlainText": "This is a test paragraph with bold and italic text.",
                    "Properties": {"Style": "Heading1"},
                    "Segments": [
                        {
                            "SegmentText": "This is a ",
                            "Properties": {"Bold": "false", "Italic": "false"},
                            "HyperlinkId": "",
                            "CommentIds": [],
                        },
                        {
                            "SegmentText": "test",
                            "Properties": {"Bold": "true", "Italic": "false"},
                            "HyperlinkId": "LINK001",
                            "CommentIds": [],
                        },
                        {
                            "SegmentText": " paragraph with ",
                            "Properties": {"Bold": "false", "Italic": "false"},
                            "HyperlinkId": "",
                            "CommentIds": [],
                        },
                        {
                            "SegmentText": "bold and italic",
                            "Properties": {"Bold": "true", "Italic": "true"},
                            "HyperlinkId": "",
                            "CommentIds": ["COMMENT001"],
                        },
                        {
                            "SegmentText": " text.",
                            "Properties": {"Bold": "false", "Italic": "false"},
                            "HyperlinkId": "",
                            "CommentIds": [],
                        },
                    ],
                },
                {
                    "ElementType": "Paragraph",
                    "ElementId": "TEST002",
                    "PlainText": "Simple paragraph without formatting.",
                    "Properties": {"Style": "Normal"},
                    "Segments": [
                        {
                            "SegmentText": "Simple paragraph without formatting.",
                            "Properties": {"Bold": "false", "Italic": "false"},
                            "HyperlinkId": "",
                            "CommentIds": [],
                        }
                    ],
                },
                {
                    "ElementType": "Table",
                    "ElementId": "TABLE001",
                    "PlainText": "Table content should be ignored",
                },
            ],
            "Annotations": {
                "Hyperlinks": {
                    "LINK001": {
                        "Uri": "https://example.com/test",
                        "Targets": ["target1", "target2"],
                    }
                },
                "Comments": {
                    "COMMENT001": {
                        "Text": "https://comment-link.com",
                        "Author": "Test Author",
                    }
                },
            },
            "NativeRevisions": {},
        }

        # Empty data for testing
        self.empty_data = {"Elements": [], "Annotations": {}, "NativeRevisions": {}}

    def test_extract_paragraphs_dicts_basic(self):
        """Test basic paragraph extraction functionality"""
        result = extract_paragraphs_xml(self.sample_data)

        # Verify return type is list
        self.assertIsInstance(result, list)
        self.assertGreater(len(result), 0)

        # Verify XML format is correct for first chunk
        try:
            root = etree.fromstring(result[0])
            self.assertEqual(root.tag, "Xml")
        except etree.XMLSyntaxError:
            self.fail("Generated XML is not valid")

    def test_extract_paragraphs_dicts_paragraph_count(self):
        """Test correct number of paragraphs extracted"""
        result = extract_paragraphs_xml(self.sample_data)
        root = etree.fromstring(result[0])  # Get first chunk

        # Should have 2 paragraphs (Table elements are ignored)
        paragraphs = root.findall("Paragraph")
        self.assertEqual(len(paragraphs), 2)

    def test_extract_paragraphs_dicts_paragraph_attributes(self):
        """Test paragraph attributes are correctly set"""
        result = extract_paragraphs_xml(self.sample_data)
        root = etree.fromstring(result[0])  # Get first chunk

        # Check first paragraph
        para1 = root.find(".//Paragraph[@paraId='TEST001']")
        self.assertIsNotNone(para1)
        self.assertEqual(para1.get("Style"), "Heading1")
        self.assertEqual(para1.text, "This is a test paragraph with bold and italic text.")

    def test_extract_paragraphs_dicts_empty_data(self):
        """Test handling of empty data"""
        result = extract_paragraphs_xml(self.empty_data)

        # Should return empty list
        self.assertEqual(len(result), 0)

    def test_extract_paragraphs_dicts_skip_empty_text(self):
        """Test skipping paragraphs with empty text"""
        data_no_text = {
            "Elements": [
                {
                    "ElementType": "Paragraph",
                    "ElementId": "EMPTY001",
                    "PlainText": "",  # Empty text
                    "Properties": {"Style": "Normal"},
                    "Segments": [],
                }
            ],
            "Annotations": {},
            "NativeRevisions": {},
        }

        result = extract_paragraphs_xml(data_no_text)

        # Empty text paragraphs should be skipped, resulting in empty list
        self.assertEqual(len(result), 0)

    def test_extract_paragraph_data_content(self):
        """Test extracting paragraph content using new unified method"""
        xml_result_list = extract_paragraphs_xml(self.sample_data)
        xml_result = xml_result_list[0]  # Get first chunk

        # Test existing paragraph
        result = extract_paragraph_data("TEST001", xml_result, self.sample_data)
        self.assertEqual(result["content"], "This is a test paragraph with bold and italic text.")

        # Test non-existing paragraph
        result = extract_paragraph_data("NONEXISTENT", xml_result, self.sample_data)
        self.assertEqual(result["content"], "")
        self.assertEqual(result["links"], [])
        self.assertEqual(result["par_style_text"], "")
        self.assertEqual(result["run_style_text"], "")

    def test_extract_paragraph_data_links(self):
        """Test extracting paragraph links using new unified method"""
        xml_result_list = extract_paragraphs_xml(self.sample_data)
        xml_result = xml_result_list[0]  # Get first chunk
        result = extract_paragraph_data("TEST001", xml_result, self.sample_data)

        links = result["links"]
        # Should have 2 links: 1 hyperlink + 1 comment URL
        self.assertEqual(len(links), 2)

        # Check hyperlinks
        hyperlinks = [item for item in links if item["Type"] == "Hyperlink"]
        self.assertEqual(len(hyperlinks), 1)
        self.assertEqual(hyperlinks[0]["Uri"], "https://example.com/test")
        self.assertEqual(hyperlinks[0]["Targets"], ["target1", "target2"])

        # Check comment URLs
        comment_urls = [item for item in links if item["Type"] == "Comment"]
        self.assertEqual(len(comment_urls), 1)
        self.assertEqual(comment_urls[0]["Uri"], "https://comment-link.com")
        self.assertEqual(comment_urls[0]["Targets"], ["COMMENT001"])

    def test_extract_paragraph_data_links_nonexistent(self):
        """Test extracting links from non-existing paragraph"""
        result = extract_paragraph_data("NONEXISTENT", "", self.sample_data)
        self.assertEqual(result["links"], [])

    def test_extract_paragraph_data_styles(self):
        """Test extracting paragraph styles using new unified method"""
        xml_result_list = extract_paragraphs_xml(self.sample_data)
        xml_result = xml_result_list[0]  # Get first chunk
        result = extract_paragraph_data("TEST001", xml_result, self.sample_data)

        # Check paragraph style
        self.assertEqual(result["par_style_text"], "<pStyle>Heading1</pStyle>")

        # Check run styles structure
        self.assertIsInstance(result["run_style_text"], str)

    def test_extract_paragraph_data_complete(self):
        """Test complete paragraph data extraction"""
        xml_result_list = extract_paragraphs_xml(self.sample_data)
        xml_result = xml_result_list[0]  # Get first chunk
        result = extract_paragraph_data("TEST001", xml_result, self.sample_data)

        # Verify all fields are present
        expected_keys = {"content", "links", "par_style_text", "run_style_text"}
        self.assertEqual(set(result.keys()), expected_keys)

        # Verify content
        self.assertEqual(result["content"], "This is a test paragraph with bold and italic text.")

        # Verify links
        self.assertEqual(len(result["links"]), 2)

        # Verify styles
        self.assertEqual(result["par_style_text"], "<pStyle>Heading1</pStyle>")
        self.assertIsInstance(result["run_style_text"], str)

    def test_convert_json_to_xml(self):
        """Test JSON to XML conversion"""
        test_data = [
            {
                "ParaId": "TEST_CONV",
                "Text": "Test conversion",
                "Style": {"Style": "Heading1"},
                "Links": [
                    {
                        "Id": "LINK_CONV",
                        "Uri": "https://test.com",
                        "Targets": ["target1"],
                    }
                ],
                "Runs": [{"Text": "Test", "style": {"Bold": True, "Italic": False}}],
            }
        ]

        result = convert_json_to_xml(test_data)
        root = etree.fromstring(result)

        # Verify basic structure
        self.assertEqual(root.tag, "Xml")
        paragraph = root.find(".//Paragraph[@paraId='TEST_CONV']")
        self.assertIsNotNone(paragraph)
        self.assertEqual(paragraph.get("Style"), "Heading1")

        # Verify links
        link = root.find(".//Link[@LinkId='LINK_CONV']")
        self.assertIsNotNone(link)
        self.assertEqual(link.get("Uri"), "https://test.com")

        # Verify run styles
        run = root.find(".//Run[@Bold='true']")
        self.assertIsNotNone(run)
        self.assertEqual(run.text, "Test")


class TestExtractParagraphDataAdvanced(unittest.TestCase):
    """Advanced test cases for the new unified extract_paragraph_data method"""

    def test_extract_paragraph_data_with_run_styles(self):
        """Test paragraph data extraction with complex run styles"""
        # Create test XML with run styles
        xml = """<Xml>
            <Paragraph paraId="STYLE_TEST" Style="Heading1">Test Content<Runs>
                    <Run Bold="true" Italic="false">Bold text</Run>
                    <Run Bold="false" Italic="true">Italic text</Run>
                    <Run Bold="true" Italic="true">Bold and Italic</Run>
                </Runs>
            </Paragraph>
        </Xml>"""

        # Test with empty xml_content to focus on XML parsing
        result = extract_paragraph_data("STYLE_TEST", xml, {})

        # Verify paragraph style
        self.assertEqual(result["par_style_text"], "<pStyle>Heading1</pStyle>")

        # Verify run styles
        expected_run = "<b>Bold text</b><i>Italic text</i><i><b>Bold and Italic</b></i>"
        self.assertEqual(result["run_style_text"], expected_run)

        # Verify content
        self.assertEqual(result["content"], "Test Content")

        # Verify links (should be empty since no xml_content provided)
        self.assertEqual(result["links"], [])

    def test_extract_paragraph_data_normal_style_skip(self):
        """Test that Normal style is skipped in paragraph style extraction"""
        xml = """<Xml>
            <Paragraph paraId="NORMAL_TEST" Style="Normal">Normal paragraph<Runs />
            </Paragraph>
        </Xml>"""

        result = extract_paragraph_data("NORMAL_TEST", xml, {})

        # Normal style should be skipped
        self.assertEqual(result["par_style_text"], "")
        self.assertEqual(result["content"], "Normal paragraph")
        self.assertEqual(result["run_style_text"], "")

    def test_extract_paragraph_data_invalid_xml(self):
        """Test handling of invalid XML"""
        invalid_xml = "<Xml><Paragraph paraId='TEST' unclosed tag"

        result = extract_paragraph_data("TEST", invalid_xml, {})

        # Should return empty values for invalid XML
        self.assertEqual(result["content"], "")
        self.assertEqual(result["par_style_text"], "")
        self.assertEqual(result["run_style_text"], "")
        self.assertEqual(result["links"], [])

    def test_extract_paragraph_data_empty_inputs(self):
        """Test handling of empty inputs"""
        # Test empty para_id
        result = extract_paragraph_data("", "<Xml></Xml>", {})
        expected_empty = {"content": "", "links": [], "par_style_text": "", "run_style_text": ""}
        self.assertEqual(result, expected_empty)


class TestInputTransformIntegration(unittest.TestCase):
    """Integration test cases with new unified method"""

    def test_full_workflow_with_unified_method(self):
        """Test complete workflow using new unified extract_paragraph_data method"""
        input_data = {
            "Elements": [
                {
                    "ElementType": "Paragraph",
                    "ElementId": "WORKFLOW001",
                    "PlainText": "Test workflow paragraph.",
                    "Properties": {"Style": "Heading2"},
                    "Segments": [
                        {
                            "SegmentText": "Test workflow",
                            "Properties": {"Bold": "true", "Italic": "false"},
                            "HyperlinkId": "WF_LINK001",
                            "CommentIds": [],
                        },
                        {
                            "SegmentText": " paragraph.",
                            "Properties": {"Bold": "false", "Italic": "false"},
                            "HyperlinkId": "",
                            "CommentIds": [],
                        },
                    ],
                }
            ],
            "Annotations": {
                "Hyperlinks": {
                    "WF_LINK001": {
                        "Uri": "https://workflow.example.com",
                        "Targets": ["wf_target"],
                    }
                },
                "Comments": {},
            },
            "NativeRevisions": {},
        }

        # Step 1: Convert to XML
        xml_result_list = extract_paragraphs_xml(input_data)
        self.assertIsInstance(xml_result_list, list)
        self.assertGreater(len(xml_result_list), 0)
        xml_result = xml_result_list[0]  # Get first chunk

        # Step 2: Extract all paragraph data using new unified method
        para_data = extract_paragraph_data("WORKFLOW001", xml_result, input_data)

        # Verify all data is extracted correctly in one call
        self.assertEqual(para_data["content"], "Test workflow paragraph.")
        self.assertEqual(len(para_data["links"]), 1)
        self.assertEqual(para_data["links"][0]["Uri"], "https://workflow.example.com")
        self.assertEqual(para_data["par_style_text"], "<pStyle>Heading2</pStyle>")
        self.assertIsInstance(para_data["run_style_text"], str)

        # Step 3: Verify XML structure
        root = etree.fromstring(xml_result)
        paragraph = root.find(".//Paragraph[@paraId='WORKFLOW001']")
        self.assertIsNotNone(paragraph)
        self.assertEqual(paragraph.get("Style"), "Heading2")

    def test_performance_comparison(self):
        """Test performance improvement with unified method"""
        import time

        # Large input data for performance testing
        input_data = {
            "Elements": [
                {
                    "ElementType": "Paragraph",
                    "ElementId": f"PERF{i:03d}",
                    "PlainText": f"Performance test paragraph {i}.",
                    "Properties": {"Style": "Normal"},
                    "Segments": [
                        {
                            "SegmentText": f"Performance test paragraph {i}.",
                            "Properties": {"Bold": "false", "Italic": "false"},
                            "HyperlinkId": "",
                            "CommentIds": [],
                        }
                    ],
                }
                for i in range(10)  # Create 10 test paragraphs
            ],
            "Annotations": {"Hyperlinks": {}, "Comments": {}},
            "NativeRevisions": {},
        }

        xml_result_list = extract_paragraphs_xml(input_data)
        xml_result = xml_result_list[0]  # Get first chunk

        # Test unified method performance
        start_time = time.time()
        for i in range(10):
            para_data = extract_paragraph_data(f"PERF{i:03d}", xml_result, input_data)
            # Verify data is extracted correctly
            self.assertIsInstance(para_data["content"], str)
            self.assertIsInstance(para_data["links"], list)
            self.assertIsInstance(para_data["par_style_text"], str)
            self.assertIsInstance(para_data["run_style_text"], str)

        unified_time = time.time() - start_time

        # The unified method should complete successfully
        # (Performance comparison would need the old separate methods to be meaningful)
        self.assertLess(unified_time, 1.0)  # Should complete within 1 second


if __name__ == "__main__":
    # Run all tests
    unittest.main(verbosity=2)

    # Or run specific test suites
    # suite = unittest.TestSuite()
    # suite.addTest(TestInputTransform('test_extract_paragraphs_dicts_basic'))
    # suite.addTest(TestExtractParagraphDataAdvanced('test_extract_paragraph_data_with_run_styles'))
    # runner = unittest.TextTestRunner(verbosity=2)
    # result = runner.run(suite)
