from typing import Any

import pytest
from base.util.comment_ops_builder2 import build_comment_ops
from base.util.segment_parser import Segment2 as Segment

def mk_seg(pid: str, idx: int, start: int, end: int) -> Segment:
    """Helper function to create Segment2 objects"""
    return Segment(f"{pid}-{idx:03d}", start, end)

def strip_id(ops):
    """Remove ID field from ops for comparison"""
    return [{k: v for k, v in op.items() if k != "id"} for op in ops]

cases: list[dict[str, Any]] = [
    # ═══════════════════════════════════════════════════════════════════════════════════
    # BASIC COMMENT OPERATIONS
    # ═══════════════════════════════════════════════════════════════════════════════════
    # 1. No change, anchor only
    {
        "name": "no-change-anchor-only",
        "old_txt": "Simple text.",
        "commented": "Simple text.[1][Quotation Check Tool]",
        "run_style_text": "Simple text.",
        "segments": [mk_seg("p1", 0, 0, len("Simple text."))],
        # "expected": [
        #     {
        #         "op": "commentAdd",
        #         "target": [{"segId": "p1-000", "range": {"start": 0, "end": 12}}],
        #         "comment": {"text": "AI suggestion: Quotation Check Tool"},
        #     }
        # ],
        "expected": [],
    },
    # 2. Single character change with comment
    {
        "name": "single-char-change",
        "old_txt": 'He said "Yes."',
        "commented": "He said 'Yes.'[1][Quotation Check Tool]",
        "run_style_text": 'He said "Yes."',
        "segments": [mk_seg("P1", 0, 0, len('He said "Yes."'))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "P1-000", "range": {"start": 8, "end": 14}}],
                "comment": {"text": "AI suggestion: Quotation Check Tool"},
            }
        ],
    },
    # 3. Multi-run comment
    {
        "name": "multi-run-comment",
        "old_txt": "Hello world",
        "commented": "Hello[1][Quotation Check Tool] world",
        "run_style_text": "Hello world",
        "segments": [mk_seg("p2", 0, 0, 6), mk_seg("p2", 1, 6, 11)],
        "expected": [],
    },
    # ═══════════════════════════════════════════════════════════════════════════════════
    # TEXT TRANSFORMATION COMMENTS
    # ═══════════════════════════════════════════════════════════════════════════════════
    # 4. Pure insertion with comment
    {
        "name": "pure-insert-with-comment",
        "old_txt": "Hello world",
        "commented": "Hello awesome world[1][Grammar Check Tool]",
        "run_style_text": "Hello world",
        "segments": [mk_seg("p3", 0, 0, 11)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "p3-000", "range": {"start": 6, "end": 14}}],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # 5. Pure deletion with comment
    {
        "name": "pure-delete-with-comment",
        "old_txt": "The World Health Organization (WHO) released a report.",
        "commented": "The World Health Organization released a report.[1][Abbreviation Check Tool]",
        "run_style_text": "The World Health Organization (WHO) released a report.",
        "segments": [mk_seg("p4", 0, 0, len("The World Health Organization (WHO) released a report."))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "p4-000", "range": {"start": 30, "end": 36}}],
                "comment": {"text": "AI suggestion: Abbreviation Check Tool"},
            }
        ],
    },
    # 6. Replacement with comment
    {
        "name": "replace-with-comment",
        "old_txt": "The application is per section 28R",
        "commented": "The application is per s 28R[1][Abbreviation Check Tool]",
        "run_style_text": "The application is per section 28R",
        "segments": [mk_seg("p5", 0, 0, len("The application is per section 28R"))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "p5-000", "range": {"start": 23, "end": 35}}],
                "comment": {"text": "AI suggestion: Abbreviation Check Tool"},
            }
        ],
    },
    # 7. Cross-run replacement
    {
        "name": "cross-run-replace",
        "old_txt": "foo bar",
        "commented": "foo baz[1][Grammar Check Tool]",
        "run_style_text": "foo bar",
        "segments": [mk_seg("p6", 0, 0, 4), mk_seg("p6", 1, 4, 7)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [
                    # {"segId": "p6-000", "range": {"start": 4, "end": 4}},
                    {"segId": "p6-001", "range": {"start": 0, "end": 3}},
                ],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # 8. Cross-run deletion
    {
        "name": "cross-run-delete",
        "old_txt": "foo bar",
        "commented": "foo[1][Grammar Check Tool]",
        "run_style_text": "foo bar",
        "segments": [mk_seg("p7", 0, 0, 4), mk_seg("p7", 1, 4, 7)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [
                    {"segId": "p7-000", "range": {"start": 4, "end": 4}},
                    {"segId": "p7-001", "range": {"start": 0, "end": 3}},
                ],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # ═══════════════════════════════════════════════════════════════════════════════════
    # STYLE COMMENTS (EMPHASIS CHECK TOOL)
    # ═══════════════════════════════════════════════════════════════════════════════════
    # 9. Italic removal
    {
        "name": "italic-removal",
        "old_txt": "This is important information.",
        "commented": "This is important information.[1][Emphasis Check Tool]",
        "run_style_text": "This is <i>important</i> information.",
        "segments": [mk_seg("e1", 0, 0, len("This is important information."))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "e1-000", "range": {"start": 8, "end": 17}}],
                "comment": {"text": "AI Suggestion: Italic should not be used in this scenario."},
            }
        ],
    },
    # 10. Bold removal
    {
        "name": "bold-removal",
        "old_txt": "It is critical to note.",
        "commented": "It is critical to note.[1][Emphasis Check Tool]",
        "run_style_text": "It is <b>critical</b> to note.",
        "segments": [mk_seg("e2", 0, 0, len("It is critical to note."))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "e2-000", "range": {"start": 6, "end": 14}}],
                "comment": {"text": "AI Suggestion: Bold should not be used in this scenario."},
            }
        ],
    },
    # 11. Underline removal
    {
        "name": "underline-removal",
        "old_txt": "Avoid underlined text in guidance notes.",
        "commented": "Avoid underlined text in guidance notes.[1][Emphasis Check Tool]",
        "run_style_text": "Avoid <u>underlined</u> text in guidance notes.",
        "segments": [mk_seg("e3", 0, 0, len("Avoid underlined text in guidance notes."))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "e3-000", "range": {"start": 6, "end": 16}}],
                "comment": {"text": "AI Suggestion: Underline should not be used in this scenario."},
            }
        ],
    },
    # 12. Valid practice tip styling (should be preserved)
    {
        "name": "valid-practice-tip",
        "old_txt": "Practice tip: This is valid styling.",
        "commented": "Practice tip: This is valid styling.[1][Emphasis Check Tool]",
        "run_style_text": "<i><u>Practice tip:</u></i> This is valid styling.",
        "segments": [mk_seg("e4", 0, 0, len("Practice tip: This is valid styling."))],
        "expected": [],
    },
    # ═══════════════════════════════════════════════════════════════════════════════════
    # EDGE CASES
    # ═══════════════════════════════════════════════════════════════════════════════════
    # 13. Unicode handling
    {
        "name": "unicode-handling",
        "old_txt": "你好，世界",
        "commented": "你好——世界[1][Grammar Check Tool]",
        "run_style_text": "你好，世界",
        "segments": [mk_seg("u1", 0, 0, len("你好，世界"))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "u1-000", "range": {"start": 2, "end": 3}}],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # 14. Multiple anchors in same paragraph
    {
        "name": "multiple-anchors",
        "old_txt": "ABC — XYZ",
        "commented": "ABC ——[1][Quotation Check Tool] XYZ[2][Abbreviation Check Tool]",
        "run_style_text": "ABC — XYZ",
        "segments": [mk_seg("m1", 0, 0, 4), mk_seg("m1", 1, 4, 6), mk_seg("m1", 2, 6, 9)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "m1-001", "range": {"start": 1, "end": 2}}],
                "comment": {"text": "AI suggestion: Quotation Check Tool"},
            },
            # {
            #     "op": "commentAdd",
            #     "target": [{"segId": "m1-002", "range": {"start": 0, "end": 3}}],
            #     "comment": {"text": "AI suggestion: Abbreviation Check Tool"},
            # },
        ],
    },
    # 15. Empty result (legislation note stripped)
    {
        "name": "legislation-note-stripped",
        "old_txt": "Hello",
        "commented": "Hello (Legislation/jurisdiction not found in solr database)[1][Legislation Check Tool]",
        "run_style_text": "Hello",
        "segments": [mk_seg("l1", 0, 0, 5)],
        "expected": [
            {
                "comment": {"text": "Legislation/jurisdiction not found"},
                "op": "commentAdd",
                "target": [{"range": {"start": 5, "end": 5}, "segId": "l1-000"}],
            }
        ],
    },
    {
        "name": "abbr-note-stripped2",
        "old_txt": "Hello",
        "commented": "Hello (wrong abbr detected)[1][Abbreviation Check Tool]",
        "run_style_text": "Hello",
        "segments": [mk_seg("l1", 0, 0, 5)],
        "expected": [
            {
                "comment": {"text": "wrong abbr detected"},
                "op": "commentAdd",
                "target": [{"range": {"start": 5, "end": 5}, "segId": "l1-000"}],
            }
        ],
    },
    # 16. Partial legislation note (with valid content)
    {
        "name": "partial-legislation-note",
        "old_txt": "Hello",
        "commented": "Hello (fullname) (Legislation/jurisdiction not found in solr database)[1][Legislation Check Tool]",
        "run_style_text": "Hello",
        "segments": [mk_seg("l2", 0, 0, 5)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "l2-000", "range": {"start": 5, "end": 16}}],
                "comment": {"text": "Legislation/jurisdiction not found"},
            }
        ],
    },
    # ═══════════════════════════════════════════════════════════════════════════════════
    # TOOL-SPECIFIC SCENARIOS
    # ═══════════════════════════════════════════════════════════════════════════════════
    # 17. Quotation hierarchy fix
    {
        "name": "quotation-hierarchy-fix",
        "old_txt": "The witness said 'Yes, sir.'",
        "commented": 'The witness said "Yes, sir."[1][Quotation Check Tool]',
        "run_style_text": 'The witness said "Yes, sir."',
        "segments": [mk_seg("q1", 0, 0, len('The witness said "Yes, sir."'))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "q1-000", "range": {"start": 17, "end": 28}}],
                "comment": {"text": "AI suggestion: Quotation Check Tool"},
            }
        ],
    },
    # 18. Missing quotation marks
    {
        "name": "missing-quotation-marks",
        "old_txt": "He said that it was true.",
        "commented": 'He said that it was "true"[1][Quotation Check Tool].',
        "run_style_text": "He said that it was true.",
        "segments": [mk_seg("q2", 0, 0, len("He said that it was true."))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "q2-000", "range": {"start": 20, "end": 25}}],
                "comment": {"text": "AI suggestion: Quotation Check Tool"},
            }
        ],
    },
    # 19. Long block quotation
    {
        "name": "long-block-quotation",
        "old_txt": 'Intro sentence: "' + "Lorem ipsum " * 30 + '"',
        "commented": "Intro sentence:\n" + "Lorem ipsum " * 30 + "[1][Quotation Check Tool]",
        "run_style_text": 'Intro sentence: "' + "Lorem ipsum " * 30 + '"',
        "segments": [mk_seg("q3", 0, 0, len('Intro sentence: "' + "Lorem ipsum " * 30 + '"'))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [
                    {
                        "segId": "q3-000",
                        "range": {"start": 16, "end": len('Intro sentence: "' + "Lorem ipsum " * 30 + '"')},
                    }
                ],
                "comment": {"text": "AI suggestion: Quotation Check Tool"},
            }
        ],
    },
    # 20. Abbreviation addition
    {
        "name": "abbreviation-addition",
        "old_txt": "Fair Work Act 2009 (Cth)",
        "commented": "Fair Work Act 2009 (Cth) (FW Act)[1][Abbreviation Check Tool]",
        "run_style_text": "Fair Work Act 2009 (Cth)",
        "segments": [mk_seg("a1", 0, 0, len("Fair Work Act 2009 (Cth)"))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "a1-000", "range": {"start": 24, "end": 33}}],
                "comment": {"text": "AI suggestion: Abbreviation Check Tool"},
            }
        ],
    },
    # 21. Abbreviation replacement
    {
        "name": "abbreviation-replacement",
        "old_txt": "Corporations Act 2001 (Cth) (CA)",
        "commented": "Corporations Act 2001 (Cth) (Corp Act)[1][Abbreviation Check Tool]",
        "run_style_text": "Corporations Act 2001 (Cth) (CA)",
        "segments": [mk_seg("a2", 0, 0, len("Corporations Act 2001 (Cth) (CA)"))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "a2-000", "range": {"start": 29, "end": 33}}],
                "comment": {"text": "AI suggestion: Abbreviation Check Tool"},
            }
        ],
    },
    # 22. Legislation year addition
    {
        "name": "legislation-year-addition",
        "old_txt": "Corporations Act (Cth)",
        "commented": "Corporations Act 2001 (Cth)[1][Legislation Check Tool]",
        "run_style_text": "Corporations Act (Cth)",
        "segments": [mk_seg("leg1", 0, 0, 17), mk_seg("leg1", 1, 17, 18), mk_seg("leg1", 2, 18, 22)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [
                    # {"segId": "leg1-000", "range": {"start": 17, "end": 22}},
                    {"segId": "leg1-001", "range": {"start": 0, "end": 5}},
                    {"segId": "leg1-002", "range": {"start": 0, "end": 4}},
                ],
                "comment": {"text": "AI suggestion: Legislation Check Tool"},
            }
        ],
    },
    # 23. Grammar/Spelling correction
    {
        "name": "grammar-spelling-correction",
        "old_txt": "They plan to organize the event.",
        "commented": "They plan to organise the event.[1][Spelling Check Tool]",
        "run_style_text": "They plan to organize the event.",
        "segments": [mk_seg("g1", 0, 0, len("They plan to organize the event."))],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "g1-000", "range": {"start": 19, "end": 20}}],
                "comment": {"text": "AI suggestion: Spelling Check Tool"},
            }
        ],
    },
    # ═══════════════════════════════════════════════════════════════════════════════════
    # BOUNDARY CONDITIONS
    # ═══════════════════════════════════════════════════════════════════════════════════
    # 24. Insert at start
    {
        "name": "insert-at-start",
        "old_txt": "BC",
        "commented": "xBC[1][Grammar Check Tool]",
        "run_style_text": "BC",
        "segments": [mk_seg("b1", 0, 0, 2)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "b1-000", "range": {"start": 0, "end": 1}}],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # 25. Insert at end
    {
        "name": "insert-at-end",
        "old_txt": "Client/Matter: -None-",
        "commented": "Client/Matter: ——None——[1][Grammar Check Tool]",
        "run_style_text": "Client/Matter: -None-",
        "segments": [mk_seg("b2", 0, 0, 21)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "b2-000", "range": {"start": 15, "end": 21}}],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # 26. Whole segment deletion
    {
        "name": "whole-segment-delete",
        "old_txt": "DELETE THIS",
        "commented": "[1][Grammar Check Tool]",
        "run_style_text": "DELETE THIS",
        "segments": [mk_seg("b3", 0, 0, 11)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "b3-000", "range": {"start": 0, "end": 11}}],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # 27. Multiple interleaving anchors
    {
        "name": "multiple-interleaving-anchors",
        "old_txt": "foobar",
        "commented": "fo[1][A Tool]o bar[2][B Tool]",
        "run_style_text": "foobar",
        "segments": [mk_seg("i1", 0, 0, 6)],
        "expected": [],
    },
    # 28. Cross-run boundary insert
    {
        "name": "cross-run-boundary-insert",
        "old_txt": "Hello world",
        "commented": "Hello awesome world[1][Grammar Check Tool]",
        "run_style_text": "Hello world",
        "segments": [mk_seg("c1", 0, 0, 6), mk_seg("c1", 1, 6, 11)],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "c1-001", "range": {"start": 0, "end": 8}}],
                "comment": {"text": "AI suggestion: Grammar Check Tool"},
            }
        ],
    },
    # 29. Complex legal text with multiple overlapping tool annotations based on Word runs
    {
        "name": "complex-legal-multiple-tools-word-runs",
        "old_txt": "An affidavit supporting the application must be filed and served within the 21-day period: s 459G(3) of the Corporations Act. See also r 2.4(1) of the applicable Corporations Rules. An application without a supporting affidavit is invalid.",
        "commented": "An affidavit supporting the application must be filed and served within the 21-day period: Section 459G(3)[1][Abbreviation Check Tool][2][Emphasis Check Tool] of the Corporations Act (Legislation/jurisdiction not found in solr database)[3][Legislation Check Tool]. See also (r 2.4(1))[4][Legislation Check Tool] of the applicable Corporations Rules. An application without a supporting affidavit is invalid.",
        "run_style_text": "An affidavit supporting the application must be filed and served within the 21-day period: <u><i>s 459G(3)</i></u> of the Corporations Act. See also r 2.4(1) of the applicable Corporations Rules. An application without a supporting affidavit is invalid.",
        "segments": [
            # mk_seg("5368F48F", 0, 0, 1),    # 换行符
            mk_seg("5368F48F", 1, 0, 91),  # 正文到冒号
            mk_seg("5368F48F", 2, 91, 100),  # "s 459G(3)" hyperlink
            mk_seg("5368F48F", 3, 100, 239),
        ],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "5368F48F-002", "range": {"start": 0, "end": 1}}],
                "comment": {"text": "AI suggestion: Abbreviation Check Tool"},
            },
            # {
            #     "op": "commentAdd",
            #     "target": [
            #         {
            #             "segId": "5368F48F-002",
            #             "range": {"start": 0, "end": 9},  # 整个 “s 459G(3)”
            #         }
            #     ],
            #     "comment": {"text": "AI Suggestion: Please check the word styling."},
            # },
            {
                "op": "commentAdd",
                "target": [{"segId": "5368F48F-003", "range": {"start": 24, "end": 24}}],
                "comment": {"text": "Legislation/jurisdiction not found"},
            },
            {
                "op": "commentAdd",
                "target": [{"segId": "5368F48F-003", "range": {"start": 35, "end": 44}}],
                "comment": {"text": "AI suggestion: Legislation Check Tool"},
            },
        ],
    },
    # 30. Heading emphasis removal
    {
        "name": "heading-emphasis-removal",
        "old_txt": "Making an application to set aside a statutory demand",
        "commented": "Making an application to set aside a statutory demand[1][Emphasis Check Tool]",
        "run_style_text": "<u><i>Making an application to set aside a statutory demand</i></u>",
        "segments": [
            # 单 run；全文 53 字符
            mk_seg("0B7DED21", 0, 0, 53),
        ],
        "expected": [
            {
                "op": "commentAdd",
                "target": [
                    {"segId": "0B7DED21-000", "range": {"start": 0, "end": 53}},
                ],
                "comment": {
                    "text": "AI Suggestion: Please check the word styling.",
                },
            }
        ],
    },
    {
        "name": "reference-style-combined",
        "old_txt": "References: Kuehne bht Keuhne v Warren Shire Council [2011] NSWDC 30",
        "commented": (
            "References:[1][Emphasis Check Tool] "
            "<i>Kuehne bht Keuhne v Warren Shire Council</i>[2][Emphasis Check Tool] "
            "[2011] NSWDC 30[3][Emphasis Check Tool]"
        ),
        "run_style_text": (
            "<b>References: </b><i>Kuehne bht Keuhne v Warren Shire Council </i><u><i>[2011] NSWDC 30</i></u>"
        ),
        "segments": [
            mk_seg("36C00B29", 0, 0, 12),  # "References: "
            mk_seg("36C00B29", 1, 12, 53),  # "Kuehne bht Keuhne v Warren Shire Council "
            mk_seg("36C00B29", 2, 53, 68),  # "[2011] NSWDC 30"
        ],
        "expected": [
            {
                "op": "commentAdd",
                "target": [{"segId": "36C00B29-000", "range": {"start": 0, "end": 12}}],
                "comment": {"text": "AI Suggestion: Bold should not be used in this scenario."},
            },
            {
                "op": "commentAdd",
                "target": [{"segId": "36C00B29-002", "range": {"start": 0, "end": 15}}],
                "comment": {"text": "AI Suggestion: Please check the word styling."},
            },
        ],
    },
    # 31. Invalid / unknown tool name ── should NOT generate any patches
    {
        "name": "invalid-toolname",
        "old_txt": "Some text",
        "commented": "Some text[1][Unknown Check Tool]",
        "run_style_text": "Some text",
        "segments": [mk_seg("inv1", 0, 0, len("Some text"))],
        "expected": [],
    },
    # 32. Victoria abbreviation + italic-emphasis combo
    {
        "name": "vic-abbr-emphasis",
        "old_txt": ("Vic — Juvenile justice — Checklist for summary offences/indictable offences triable summarily"),
        "commented": (
            "Victoria[1][Abbreviation Check Tool] — Juvenile justice — Checklist "
            "for summary offences/indictable offences triable summarily"
            "[2][Emphasis Check Tool]"
        ),
        "run_style_text": (
            "<i>Vic — Juvenile justice — Checklist for summary offences/indictable "
            "offences triable </i><i>summarily</i><i></i>"
        ),
        "segments": [
            mk_seg("145BD939", 0, 0, 84),  # "Vic — … triable "
            mk_seg("145BD939", 1, 84, 93),  # "summarily"
        ],
        "expected": [
            {  # “Vic” →  “Victoria” 触发 Abbreviation Check
                "op": "commentAdd",
                "target": [{"segId": "145BD939-000", "range": {"start": 3, "end": 8}}],
                "comment": {"text": "AI suggestion: Abbreviation Check Tool"},
            },
            {  # 末尾 <i>summarily</i> 触发 Emphasis Check
                "op": "commentAdd",
                "target": [{"segId": "145BD939-001", "range": {"start": 0, "end": 9}}],
                "comment": {"text": "AI Suggestion: Italic should not be used in this scenario."},
            },
        ],
    },
    # 33. Vic + wrong-abbr note ＆ italic removal
    {
        "name": "vic-wrong-abbr-emphasis",
        "old_txt": (
            "Vic — Checklist for young person client interview  "  # 原始文本，两空格结尾
        ),
        "commented": (
            "Vic(wrong abbr detected)[1][Abbreviation Check Tool] — "
            "Checklist for young person client interview[2][Emphasis Check Tool]"
        ),
        "run_style_text": ("<i>Vic — Checklist for young person client </i><i>interview</i><i>  </i>"),
        "segments": [
            mk_seg("43D1ED81", 0, 0, 41),  # "Vic — … client "
            mk_seg("43D1ED81", 1, 41, 50),  # "interview"
            mk_seg("43D1ED81", 2, 50, 52),  # 尾部两个空格
        ],
        "expected": [
            {  # wrong-abbr 诊断（插在 “Vic” 后）
                "op": "commentAdd",
                "target": [{"segId": "43D1ED81-000", "range": {"start": 3, "end": 3}}],
                "comment": {"text": "wrong abbr detected"},
            },
            
            {  # 去除斜体 “interview”
                "op": "commentAdd",
                "target": [{"segId": "43D1ED81-001", "range": {"start": 0, "end": 9}}],
                "comment": {"text": "AI Suggestion: Italic should not be used in this scenario."},
            },
        ],
    },
    # 33. Vic + wrong-abbr note & italic removal (fixed segment ranges)
{
    "name": "vic-wrong-abbr-emphasis",
    "old_txt": (
        "Vic — Checklist for young person client interview  "
    ),
    "commented": (
        "Vic(wrong abbr detected)[1][Abbreviation Check Tool] — "
        "Checklist for young person client interview[2][Emphasis Check Tool]"
    ),
    "run_style_text": (
        "<i>Vic — Checklist for young person client </i>"
        "<i>interview</i><i>  </i>"
    ),
    # three Word runs: [client␠] [interview] [␠␠]
    "segments": [
        mk_seg("43D1ED81", 0, 0, 40),   # “Vic — … client ”
        mk_seg("43D1ED81", 1, 40, 49),  # “interview”
        mk_seg("43D1ED81", 2, 49, 51),  # two trailing spaces
    ],
    "expected": [
        {   # wrong-abbr note
            "op": "commentAdd",
            "target": [{"segId": "43D1ED81-000", "range": {"start": 3, "end": 3}}],
            "comment": {"text": "wrong abbr detected"},
        },
        {   # italic removal（11 + 2 个字符）
            "op": "commentAdd",
            "target": [
                {"segId": "43D1ED81-001", "range": {"start": 0, "end": 11}},  # interview␠␠
                {"segId": "43D1ED81-002", "range": {"start": 0, "end": 2}},   # extra spaces
            ],
            "comment": {
                "text": "AI Suggestion: Italic should not be used in this scenario."
            },
        },
    ],
},

]

@pytest.mark.parametrize("case", cases, ids=[c["name"] for c in cases])
def test_build_comment_ops_comprehensive(case):
    """Test comment operations builder with comprehensive scenarios"""
    got = build_comment_ops(
        case["old_txt"],
        case["commented"], 
        case["run_style_text"],
        case["segments"]
    )
    
    got_without_id = strip_id(got)
    assert got_without_id == case["expected"], f"Failed for case: {case['name']}"
