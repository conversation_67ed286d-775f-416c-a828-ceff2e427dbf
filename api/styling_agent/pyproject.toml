[tool.poetry]
name = "base"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.10, <3.13"
common = { path = "../common", develop = false }
pyyaml = "6.0.2"
requests = "2.32.3"
llm-proxy = { version = "0.22.0", source = "proxy" }
pymysql = "^1.1.1"
python-dotenv = "^1.1.0"
merge3 = "^0.0.15"
python-multipart = "^0.0.20"
dnspython = "^2.7.0"

[tool.poetry.group.dev.dependencies]
python-docx = "^1.2.0"
lxml = "^5.4.0"
openpyxl = "^3.1.5"
matplotlib = "^3.10.3"
jupyterlab = "^4.4.4"
pandas = "^2.3.0"
seaborn = "^0.13.2"

[tool.poetry.group.test.dependencies]
pytest = "^8.3.5"
pytest-cov = "^6.1.1"
pytest-mock = "^3.14.0"
hypothesis = "^6.131.19"

[[tool.poetry.source]]
name = "proxy"
url = "https://cert-proxy-api.search.use1.dev-fos.nl.lexis.com/dist/"
priority = "explicit"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
ignore = ["T201"]
